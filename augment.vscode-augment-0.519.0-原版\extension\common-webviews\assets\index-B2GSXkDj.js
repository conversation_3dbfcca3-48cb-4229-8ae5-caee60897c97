import{af as W,am as D,E as G,an as H,ao as I,ap as J,L as K,aq as N,ar as Q,as as R,q as V,at as b,h as U,g as A,au as X,av as Y,aw as Z}from"./SpinnerAugment-C3d3R_8C.js";const h={tick:a=>requestAnimationFrame(a),now:()=>performance.now(),tasks:new Set};function P(){const a=h.now();h.tasks.forEach(t=>{t.c(a)||(h.tasks.delete(t),t.f())}),h.tasks.size!==0&&h.tick(P)}function k(a,t){Q(()=>{a.dispatchEvent(new CustomEvent(t))})}function j(a){if(a==="float")return"cssFloat";if(a==="offset")return"cssOffset";if(a.startsWith("--"))return a;const t=a.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(n=>n[0].toUpperCase()+n.slice(1)).join("")}function L(a){const t={},n=a.split(";");for(const o of n){const[d,r]=o.split(":");if(!d||r===void 0)break;t[j(d.trim())]=r.trim()}return t}const z=a=>a;function rt(a,t,n,o){var d,r,i,u=!!(a&Y),$=!!(a&Z),l=!!(a&N),s=u&&$?"both":u?"in":"out",m=t.inert,g=t.style.overflow;function y(){var c=X,x=W;U(null),A(null);try{return d??(d=n()(t,(o==null?void 0:o())??{},{direction:s}))}finally{U(c),A(x)}}var v={is_global:l,in(){var c;if(t.inert=m,!u)return i==null||i.abort(),void((c=i==null?void 0:i.reset)==null?void 0:c.call(i));$||(r==null||r.abort()),k(t,"introstart"),r=C(t,y(),i,1,()=>{k(t,"introend"),r==null||r.abort(),r=d=void 0,t.style.overflow=g})},out(c){if(!$)return c==null||c(),void(d=void 0);t.inert=!0,k(t,"outrostart"),i=C(t,y(),r,0,()=>{k(t,"outroend"),c==null||c()})},stop:()=>{r==null||r.abort(),i==null||i.abort()}},f=W;if((f.transitions??(f.transitions=[])).push(v),u&&D){var p=l;if(!p){for(var e=f.parent;e&&e.f&G;)for(;(e=e.parent)&&!(e.f&H););p=!e||!!(e.f&I)}p&&J(()=>{K(()=>v.in())})}}function C(a,t,n,o,d){var r=o===1;if(R(t)){var i,u=!1;return V(()=>{if(!u){var p=t({direction:r?"in":"out"});i=C(a,p,n,o,d)}}),{abort:()=>{u=!0,i==null||i.abort()},deactivate:()=>i.deactivate(),reset:()=>i.reset(),t:()=>i.t()}}if(n==null||n.deactivate(),!(t!=null&&t.duration))return d(),{abort:b,deactivate:b,reset:b,t:()=>o};const{delay:$=0,css:l,tick:s,easing:m=z}=t;var g=[];if(r&&n===void 0&&(s&&s(0,1),l)){var y=L(l(0,1));g.push(y,y)}var v=()=>1-o,f=a.animate(g,{duration:$,fill:"forwards"});return f.onfinish=()=>{f.cancel();var p=(n==null?void 0:n.t())??1-o;n==null||n.abort();var e=o-p,c=t.duration*Math.abs(e),x=[];if(c>0){var S=!1;if(l)for(var E=Math.ceil(c/(1e3/60)),F=0;F<=E;F+=1){var q=p+e*m(F/E),M=L(l(q,1-q));x.push(M),S||(S=M.overflow==="hidden")}S&&(a.style.overflow="hidden"),v=()=>{var w=f.currentTime;return p+e*m(w/c)},s&&function(w){let _;h.tasks.size===0&&h.tick(P),new Promise(B=>{h.tasks.add(_={c:w,f:B})})}(()=>{if(f.playState!=="running")return!1;var w=v();return s(w,1-w),!0})}(f=a.animate(x,{duration:c,fill:"forwards"})).onfinish=()=>{v=()=>o,s==null||s(o,1-o),d()}},{abort:()=>{f&&(f.cancel(),f.effect=null,f.onfinish=b)},deactivate:()=>{d=b},reset:()=>{o===0&&(s==null||s(1,0))},t:()=>v()}}const tt=a=>a;function T(a){const t=a-1;return t*t*t+1}function O(a){const t=typeof a=="string"&&a.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[a,"px"]}function it(a,{delay:t=0,duration:n=400,easing:o=tt}={}){const d=+getComputedStyle(a).opacity;return{delay:t,duration:n,easing:o,css:r=>"opacity: "+r*d}}function nt(a,{delay:t=0,duration:n=400,easing:o=T,x:d=0,y:r=0,opacity:i=0}={}){const u=getComputedStyle(a),$=+u.opacity,l=u.transform==="none"?"":u.transform,s=$*(1-i),[m,g]=O(d),[y,v]=O(r);return{delay:t,duration:n,easing:o,css:(f,p)=>`
			transform: ${l} translate(${(1-f)*m}${g}, ${(1-f)*y}${v});
			opacity: ${$-s*p}`}}function et(a,{delay:t=0,duration:n=400,easing:o=T,axis:d="y"}={}){const r=getComputedStyle(a),i=+r.opacity,u=d==="y"?"height":"width",$=parseFloat(r[u]),l=d==="y"?["top","bottom"]:["left","right"],s=l.map(e=>`${e[0].toUpperCase()}${e.slice(1)}`),m=parseFloat(r[`padding${s[0]}`]),g=parseFloat(r[`padding${s[1]}`]),y=parseFloat(r[`margin${s[0]}`]),v=parseFloat(r[`margin${s[1]}`]),f=parseFloat(r[`border${s[0]}Width`]),p=parseFloat(r[`border${s[1]}Width`]);return{delay:t,duration:n,easing:o,css:e=>`overflow: hidden;opacity: ${Math.min(20*e,1)*i};${u}: ${e*$}px;padding-${l[0]}: ${e*m}px;padding-${l[1]}: ${e*g}px;margin-${l[0]}: ${e*y}px;margin-${l[1]}: ${e*v}px;border-${l[0]}-width: ${e*f}px;border-${l[1]}-width: ${e*p}px;min-${u}: 0`}}export{it as a,nt as f,et as s,rt as t};
