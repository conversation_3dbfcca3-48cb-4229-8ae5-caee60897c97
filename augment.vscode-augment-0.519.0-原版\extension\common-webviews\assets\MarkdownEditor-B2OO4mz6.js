import{l as b,A as U,C as s,a5 as W,D as Y,J as T,G as ee,N as g,t as o,M as r,a6 as te,H as I,Z as $,m as V,b as f,I as se,X as h,T as D,S as F,O as ae,V as re}from"./SpinnerAugment-C3d3R_8C.js";import"./IconButtonAugment-BYROpfM6.js";import"./BaseTextInput-hjj6nBZd.js";import{T as ne}from"./TextAreaAugment-BGi_2d0z.js";import{l as oe}from"./chevron-down-CsTV-WoH.js";var ce=T('<div class="l-markdown-editor svelte-1dcrmc3"><div class="c-markdown-editor__header svelte-1dcrmc3"><!></div> <!> <div class="c-markdown-editor__content svelte-1dcrmc3"><!> <!></div></div> <div class="c-markdown-editor__status svelte-1dcrmc3"><!> <!></div>',1);function me(K,t){const M=b(t,["children","$$slots","$$events","$$legacy"]),A=b(M,["variant","size","color","resize","textInput","value","selectedText","selectionStart","selectionEnd","saveFunction","debounceValue"]);U(t,!1);let y,C=s(t,"variant",8,"surface"),G=s(t,"size",8,2),H=s(t,"color",24,()=>{}),J=s(t,"resize",8,"none"),a=s(t,"textInput",28,()=>{}),c=s(t,"value",12,""),k=s(t,"selectedText",12,""),l=s(t,"selectionStart",12,0),i=s(t,"selectionEnd",12,0),N=s(t,"saveFunction",8),O=s(t,"debounceValue",8,2500),d=V(!1),u=V();const v=async()=>{try{N()(),h(d,!0),clearTimeout(y),y=setTimeout(()=>{h(d,!1)},1500)}catch(e){h(u,e instanceof Error?e.message:String(e))}};function m(){a()&&(l(a().selectionStart),i(a().selectionEnd),l()!==i()?k(c().substring(l(),i())):k(""))}const X=oe.debounce(v,O());W(()=>{v()}),Y();var w=ce(),z=ee(w),x=o(z),Z=o(x);g(Z,t,"header",{},null);var E=r(x,2);g(E,t,"default",{},null);var j=r(E,2),S=o(j);g(S,t,"title",{},null);var q=r(S,2);ne(q,te({get variant(){return C()},get size(){return G()},get color(){return H()},get resize(){return J()},placeholder:"Enter markdown content..."},()=>A,{get textInput(){return a()},set textInput(e){a(e)},get value(){return c()},set value(e){c(e)},$$events:{select:m,mouseup:m,keyup:()=>{m()},input:X,keydown:e=>{(e.key==="Escape"||(e.metaKey||e.ctrlKey)&&e.key==="s")&&(e.preventDefault(),v())}},$$legacy:!0}));var B=r(z,2),_=o(B),L=e=>{D(e,{size:1,weight:"light",color:"error",children:(p,R)=>{var n=F();ae(()=>re(n,$(u))),f(p,n)},$$slots:{default:!0}})};I(_,e=>{$(u)&&e(L)});var P=r(_,2),Q=e=>{D(e,{size:1,weight:"light",color:"success",children:(p,R)=>{var n=F("Saved");f(p,n)},$$slots:{default:!0}})};I(P,e=>{$(d)&&e(Q)}),f(K,w),se()}export{me as M};
