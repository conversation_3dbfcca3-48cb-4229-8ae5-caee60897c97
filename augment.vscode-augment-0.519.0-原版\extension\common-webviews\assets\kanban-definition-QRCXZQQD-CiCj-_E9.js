import{_ as a,l as q,c as H,I as gt,ag as pt,ah as ut,ai as dt,aa as yt,G as K,i as B,y as mt,J as ft,ab as _t,ac as ot,ad as at}from"./AugmentMessage-B21_KOYl.js";import"./SpinnerAugment-C3d3R_8C.js";import"./IconButtonAugment-BYROpfM6.js";import"./CalloutAugment-C1fpFxhd.js";import"./CardAugment-L1_52yiK.js";import"./index-BWYp8-tu.js";import"./async-messaging-BeBg25ZO.js";import"./message-broker-BVKpqRQ5.js";import"./types-CGlLNakm.js";import"./file-paths-BEJIrsZp.js";import"./isObjectLike-DEzymPim.js";import"./BaseTextInput-hjj6nBZd.js";import"./folder-opened-AFaV2qWu.js";import"./index-BiRO5qTg.js";import"./diff-operations-D22Y9QvN.js";import"./svelte-component-ClwSdZAs.js";import"./Filespan-BdXbs49f.js";import"./toggleHighContrast-Cb9MCs64.js";import"./preload-helper-Dv6uf1Os.js";import"./index-BPBpJFCC.js";import"./keypress-DD1aQVr0.js";import"./await-XsDZ1KjX.js";import"./OpenFileButton-CMz0rieg.js";import"./chat-context-DL_54yja.js";import"./index-C4gKbsWy.js";import"./remote-agents-client-Bz3pE78Q.js";import"./ra-diff-ops-model-D15ceLKW.js";import"./TextAreaAugment-BGi_2d0z.js";import"./ButtonAugment-Cfj8nOxE.js";import"./CollapseButtonAugment-ClJpCixO.js";import"./user-BS1qxHV9.js";import"./MaterialIcon-Ce6mYTQ4.js";import"./CopyButton-DDb8u9Qe.js";import"./ellipsis-v5eG5a7L.js";import"./IconFilePath-J6j-AVyQ.js";import"./LanguageIcon-Byw9lw0X.js";import"./next-edit-types-904A5ehg.js";import"./chevron-down-CsTV-WoH.js";import"./index-B2GSXkDj.js";import"./augment-logo-BwZlCwrx.js";import"./pen-to-square-DGRIEioi.js";import"./check-BsVXgaKr.js";var Q=function(){var t=a(function(e,l,o,r){for(o=o||{},r=e.length;r--;o[e[r]]=l);return o},"o"),u=[1,4],p=[1,13],s=[1,12],m=[1,15],d=[1,16],g=[1,20],y=[1,19],D=[6,7,8],v=[1,26],x=[1,24],A=[1,25],i=[6,7,11],G=[1,31],L=[6,7,11,24],F=[1,6,13,16,17,20,23],U=[1,35],M=[1,36],w=[1,6,7,11,13,16,17,20,23],E=[1,38],I={trace:a(function(){},"trace"),yy:{},symbols_:{error:2,start:3,mindMap:4,spaceLines:5,SPACELINE:6,NL:7,KANBAN:8,document:9,stop:10,EOF:11,statement:12,SPACELIST:13,node:14,shapeData:15,ICON:16,CLASS:17,nodeWithId:18,nodeWithoutId:19,NODE_DSTART:20,NODE_DESCR:21,NODE_DEND:22,NODE_ID:23,SHAPE_DATA:24,$accept:0,$end:1},terminals_:{2:"error",6:"SPACELINE",7:"NL",8:"KANBAN",11:"EOF",13:"SPACELIST",16:"ICON",17:"CLASS",20:"NODE_DSTART",21:"NODE_DESCR",22:"NODE_DEND",23:"NODE_ID",24:"SHAPE_DATA"},productions_:[0,[3,1],[3,2],[5,1],[5,2],[5,2],[4,2],[4,3],[10,1],[10,1],[10,1],[10,2],[10,2],[9,3],[9,2],[12,3],[12,2],[12,2],[12,2],[12,1],[12,2],[12,1],[12,1],[12,1],[12,1],[14,1],[14,1],[19,3],[18,1],[18,4],[15,2],[15,1]],performAction:a(function(e,l,o,r,h,n,j){var c=n.length-1;switch(h){case 6:case 7:return r;case 8:r.getLogger().trace("Stop NL ");break;case 9:r.getLogger().trace("Stop EOF ");break;case 11:r.getLogger().trace("Stop NL2 ");break;case 12:r.getLogger().trace("Stop EOF2 ");break;case 15:r.getLogger().info("Node: ",n[c-1].id),r.addNode(n[c-2].length,n[c-1].id,n[c-1].descr,n[c-1].type,n[c]);break;case 16:r.getLogger().info("Node: ",n[c].id),r.addNode(n[c-1].length,n[c].id,n[c].descr,n[c].type);break;case 17:r.getLogger().trace("Icon: ",n[c]),r.decorateNode({icon:n[c]});break;case 18:case 23:r.decorateNode({class:n[c]});break;case 19:r.getLogger().trace("SPACELIST");break;case 20:r.getLogger().trace("Node: ",n[c-1].id),r.addNode(0,n[c-1].id,n[c-1].descr,n[c-1].type,n[c]);break;case 21:r.getLogger().trace("Node: ",n[c].id),r.addNode(0,n[c].id,n[c].descr,n[c].type);break;case 22:r.decorateNode({icon:n[c]});break;case 27:r.getLogger().trace("node found ..",n[c-2]),this.$={id:n[c-1],descr:n[c-1],type:r.getType(n[c-2],n[c])};break;case 28:this.$={id:n[c],descr:n[c],type:0};break;case 29:r.getLogger().trace("node found ..",n[c-3]),this.$={id:n[c-3],descr:n[c-1],type:r.getType(n[c-2],n[c])};break;case 30:this.$=n[c-1]+n[c];break;case 31:this.$=n[c]}},"anonymous"),table:[{3:1,4:2,5:3,6:[1,5],8:u},{1:[3]},{1:[2,1]},{4:6,6:[1,7],7:[1,8],8:u},{6:p,7:[1,10],9:9,12:11,13:s,14:14,16:m,17:d,18:17,19:18,20:g,23:y},t(D,[2,3]),{1:[2,2]},t(D,[2,4]),t(D,[2,5]),{1:[2,6],6:p,12:21,13:s,14:14,16:m,17:d,18:17,19:18,20:g,23:y},{6:p,9:22,12:11,13:s,14:14,16:m,17:d,18:17,19:18,20:g,23:y},{6:v,7:x,10:23,11:A},t(i,[2,24],{18:17,19:18,14:27,16:[1,28],17:[1,29],20:g,23:y}),t(i,[2,19]),t(i,[2,21],{15:30,24:G}),t(i,[2,22]),t(i,[2,23]),t(L,[2,25]),t(L,[2,26]),t(L,[2,28],{20:[1,32]}),{21:[1,33]},{6:v,7:x,10:34,11:A},{1:[2,7],6:p,12:21,13:s,14:14,16:m,17:d,18:17,19:18,20:g,23:y},t(F,[2,14],{7:U,11:M}),t(w,[2,8]),t(w,[2,9]),t(w,[2,10]),t(i,[2,16],{15:37,24:G}),t(i,[2,17]),t(i,[2,18]),t(i,[2,20],{24:E}),t(L,[2,31]),{21:[1,39]},{22:[1,40]},t(F,[2,13],{7:U,11:M}),t(w,[2,11]),t(w,[2,12]),t(i,[2,15],{24:E}),t(L,[2,30]),{22:[1,41]},t(L,[2,27]),t(L,[2,29])],defaultActions:{2:[2,1],6:[2,2]},parseError:a(function(e,l){if(!l.recoverable){var o=new Error(e);throw o.hash=l,o}this.trace(e)},"parseError"),parse:a(function(e){var l=this,o=[0],r=[],h=[null],n=[],j=this.table,c="",W=0,et=0,lt=n.slice.call(arguments,1),_=Object.create(this.lexer),T={yy:{}};for(var J in this.yy)Object.prototype.hasOwnProperty.call(this.yy,J)&&(T.yy[J]=this.yy[J]);_.setInput(e,T.yy),T.yy.lexer=_,T.yy.parser=this,_.yylloc===void 0&&(_.yylloc={});var Y=_.yylloc;n.push(Y);var ht=_.options&&_.options.ranges;function nt(){var S;return typeof(S=r.pop()||_.lex()||1)!="number"&&(S instanceof Array&&(S=(r=S).pop()),S=l.symbols_[S]||S),S}typeof T.yy.parseError=="function"?this.parseError=T.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError,a(function(S){o.length=o.length-2*S,h.length=h.length-S,n.length=n.length-S},"popStack"),a(nt,"lex");for(var b,R,k,it,z,C,st,X,P={};;){if(R=o[o.length-1],this.defaultActions[R]?k=this.defaultActions[R]:(b==null&&(b=nt()),k=j[R]&&j[R][b]),k===void 0||!k.length||!k[0]){var rt="";for(z in X=[],j[R])this.terminals_[z]&&z>2&&X.push("'"+this.terminals_[z]+"'");rt=_.showPosition?"Parse error on line "+(W+1)+`:
`+_.showPosition()+`
Expecting `+X.join(", ")+", got '"+(this.terminals_[b]||b)+"'":"Parse error on line "+(W+1)+": Unexpected "+(b==1?"end of input":"'"+(this.terminals_[b]||b)+"'"),this.parseError(rt,{text:_.match,token:this.terminals_[b]||b,line:_.yylineno,loc:Y,expected:X})}if(k[0]instanceof Array&&k.length>1)throw new Error("Parse Error: multiple actions possible at state: "+R+", token: "+b);switch(k[0]){case 1:o.push(b),h.push(_.yytext),n.push(_.yylloc),o.push(k[1]),b=null,et=_.yyleng,c=_.yytext,W=_.yylineno,Y=_.yylloc;break;case 2:if(C=this.productions_[k[1]][1],P.$=h[h.length-C],P._$={first_line:n[n.length-(C||1)].first_line,last_line:n[n.length-1].last_line,first_column:n[n.length-(C||1)].first_column,last_column:n[n.length-1].last_column},ht&&(P._$.range=[n[n.length-(C||1)].range[0],n[n.length-1].range[1]]),(it=this.performAction.apply(P,[c,et,W,T.yy,k[1],h,n].concat(lt)))!==void 0)return it;C&&(o=o.slice(0,-1*C*2),h=h.slice(0,-1*C),n=n.slice(0,-1*C)),o.push(this.productions_[k[1]][0]),h.push(P.$),n.push(P._$),st=j[o[o.length-2]][o[o.length-1]],o.push(st);break;case 3:return!0}}return!0},"parse")},O=function(){return{EOF:1,parseError:a(function(e,l){if(!this.yy.parser)throw new Error(e);this.yy.parser.parseError(e,l)},"parseError"),setInput:a(function(e,l){return this.yy=l||this.yy||{},this._input=e,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},"setInput"),input:a(function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e,e.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e},"input"),unput:a(function(e){var l=e.length,o=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-l),this.offset-=l;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),o.length-1&&(this.yylineno-=o.length-1);var h=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:o?(o.length===r.length?this.yylloc.first_column:0)+r[r.length-o.length].length-o[0].length:this.yylloc.first_column-l},this.options.ranges&&(this.yylloc.range=[h[0],h[0]+this.yyleng-l]),this.yyleng=this.yytext.length,this},"unput"),more:a(function(){return this._more=!0,this},"more"),reject:a(function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+`. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"reject"),less:a(function(e){this.unput(this.match.slice(e))},"less"),pastInput:a(function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")},"pastInput"),upcomingInput:a(function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")},"upcomingInput"),showPosition:a(function(){var e=this.pastInput(),l=new Array(e.length+1).join("-");return e+this.upcomingInput()+`
`+l+"^"},"showPosition"),test_match:a(function(e,l){var o,r,h;if(this.options.backtrack_lexer&&(h={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(h.yylloc.range=this.yylloc.range.slice(0))),(r=e[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],o=this.performAction.call(this,this.yy,this,l,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),o)return o;if(this._backtrack){for(var n in h)this[n]=h[n];return!1}return!1},"test_match"),next:a(function(){if(this.done)return this.EOF;var e,l,o,r;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var h=this._currentRules(),n=0;n<h.length;n++)if((o=this._input.match(this.rules[h[n]]))&&(!l||o[0].length>l[0].length)){if(l=o,r=n,this.options.backtrack_lexer){if((e=this.test_match(o,h[n]))!==!1)return e;if(this._backtrack){l=!1;continue}return!1}if(!this.options.flex)break}return l?(e=this.test_match(l,h[r]))!==!1&&e:this._input===""?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+`. Unrecognized text.
`+this.showPosition(),{text:"",token:null,line:this.yylineno})},"next"),lex:a(function(){var e=this.next();return e||this.lex()},"lex"),begin:a(function(e){this.conditionStack.push(e)},"begin"),popState:a(function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},"popState"),_currentRules:a(function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},"_currentRules"),topState:a(function(e){return(e=this.conditionStack.length-1-Math.abs(e||0))>=0?this.conditionStack[e]:"INITIAL"},"topState"),pushState:a(function(e){this.begin(e)},"pushState"),stateStackSize:a(function(){return this.conditionStack.length},"stateStackSize"),options:{"case-insensitive":!0},performAction:a(function(e,l,o,r){switch(o){case 0:return this.pushState("shapeData"),l.yytext="",24;case 1:return this.pushState("shapeDataStr"),24;case 2:return this.popState(),24;case 3:const h=/\n\s*/g;return l.yytext=l.yytext.replace(h,"<br/>"),24;case 4:return 24;case 5:case 10:case 29:case 32:this.popState();break;case 6:return e.getLogger().trace("Found comment",l.yytext),6;case 7:return 8;case 8:this.begin("CLASS");break;case 9:return this.popState(),17;case 11:e.getLogger().trace("Begin icon"),this.begin("ICON");break;case 12:return e.getLogger().trace("SPACELINE"),6;case 13:return 7;case 14:return 16;case 15:e.getLogger().trace("end icon"),this.popState();break;case 16:return e.getLogger().trace("Exploding node"),this.begin("NODE"),20;case 17:return e.getLogger().trace("Cloud"),this.begin("NODE"),20;case 18:return e.getLogger().trace("Explosion Bang"),this.begin("NODE"),20;case 19:return e.getLogger().trace("Cloud Bang"),this.begin("NODE"),20;case 20:case 21:case 22:case 23:return this.begin("NODE"),20;case 24:return 13;case 25:return 23;case 26:return 11;case 27:this.begin("NSTR2");break;case 28:return"NODE_DESCR";case 30:e.getLogger().trace("Starting NSTR"),this.begin("NSTR");break;case 31:return e.getLogger().trace("description:",l.yytext),"NODE_DESCR";case 33:return this.popState(),e.getLogger().trace("node end ))"),"NODE_DEND";case 34:return this.popState(),e.getLogger().trace("node end )"),"NODE_DEND";case 35:return this.popState(),e.getLogger().trace("node end ...",l.yytext),"NODE_DEND";case 36:case 39:case 40:return this.popState(),e.getLogger().trace("node end (("),"NODE_DEND";case 37:case 38:return this.popState(),e.getLogger().trace("node end (-"),"NODE_DEND";case 41:case 42:return e.getLogger().trace("Long description:",l.yytext),21}},"anonymous"),rules:[/^(?:@\{)/i,/^(?:["])/i,/^(?:["])/i,/^(?:[^\"]+)/i,/^(?:[^}^"]+)/i,/^(?:\})/i,/^(?:\s*%%.*)/i,/^(?:kanban\b)/i,/^(?::::)/i,/^(?:.+)/i,/^(?:\n)/i,/^(?:::icon\()/i,/^(?:[\s]+[\n])/i,/^(?:[\n]+)/i,/^(?:[^\)]+)/i,/^(?:\))/i,/^(?:-\))/i,/^(?:\(-)/i,/^(?:\)\))/i,/^(?:\))/i,/^(?:\(\()/i,/^(?:\{\{)/i,/^(?:\()/i,/^(?:\[)/i,/^(?:[\s]+)/i,/^(?:[^\(\[\n\)\{\}@]+)/i,/^(?:$)/i,/^(?:["][`])/i,/^(?:[^`"]+)/i,/^(?:[`]["])/i,/^(?:["])/i,/^(?:[^"]+)/i,/^(?:["])/i,/^(?:[\)]\))/i,/^(?:[\)])/i,/^(?:[\]])/i,/^(?:\}\})/i,/^(?:\(-)/i,/^(?:-\))/i,/^(?:\(\()/i,/^(?:\()/i,/^(?:[^\)\]\(\}]+)/i,/^(?:.+(?!\(\())/i],conditions:{shapeDataEndBracket:{rules:[],inclusive:!1},shapeDataStr:{rules:[2,3],inclusive:!1},shapeData:{rules:[1,4,5],inclusive:!1},CLASS:{rules:[9,10],inclusive:!1},ICON:{rules:[14,15],inclusive:!1},NSTR2:{rules:[28,29],inclusive:!1},NSTR:{rules:[31,32],inclusive:!1},NODE:{rules:[27,30,33,34,35,36,37,38,39,40,41,42],inclusive:!1},INITIAL:{rules:[0,6,7,8,11,12,13,16,17,18,19,20,21,22,23,24,25,26],inclusive:!0}}}}();function $(){this.yy={}}return I.lexer=O,a($,"Parser"),$.prototype=I,I.Parser=$,new $}();Q.parser=Q;var Et=Q,N=[],tt=[],V=0,Z={},bt=a(()=>{N=[],tt=[],V=0,Z={}},"clear"),kt=a(t=>{if(N.length===0)return null;const u=N[0].level;let p=null;for(let s=N.length-1;s>=0;s--)if(N[s].level!==u||p||(p=N[s]),N[s].level<u)throw new Error('Items without section detected, found section ("'+N[s].label+'")');return t===(p==null?void 0:p.level)?null:p},"getSection"),ct=a(function(){return tt},"getSections"),St=a(function(){const t=[],u=ct(),p=H();for(const s of u){const m={id:s.id,label:B(s.label??"",p),isGroup:!0,ticket:s.ticket,shape:"kanbanSection",level:s.level,look:p.look};t.push(m);const d=N.filter(g=>g.parentId===s.id);for(const g of d){const y={id:g.id,parentId:s.id,label:B(g.label??"",p),isGroup:!1,ticket:g==null?void 0:g.ticket,priority:g==null?void 0:g.priority,assigned:g==null?void 0:g.assigned,icon:g==null?void 0:g.icon,shape:"kanbanItem",level:g.level,rx:5,ry:5,cssStyles:["text-align: left"]};t.push(y)}}return{nodes:t,edges:[],other:{},config:H()}},"getData"),Nt=a((t,u,p,s,m)=>{var v,x;const d=H();let g=((v=d.mindmap)==null?void 0:v.padding)??K.mindmap.padding;switch(s){case f.ROUNDED_RECT:case f.RECT:case f.HEXAGON:g*=2}const y={id:B(u,d)||"kbn"+V++,level:t,label:B(p,d),width:((x=d.mindmap)==null?void 0:x.maxNodeWidth)??K.mindmap.maxNodeWidth,padding:g,isGroup:!1};if(m!==void 0){let A;A=m.includes(`
`)?m+`
`:`{
`+m+`
}`;const i=mt(A,{schema:ft});if(i.shape&&(i.shape!==i.shape.toLowerCase()||i.shape.includes("_")))throw new Error(`No such shape: ${i.shape}. Shape names should be lowercase.`);i!=null&&i.shape&&i.shape==="kanbanItem"&&(y.shape=i==null?void 0:i.shape),i!=null&&i.label&&(y.label=i==null?void 0:i.label),i!=null&&i.icon&&(y.icon=i==null?void 0:i.icon.toString()),i!=null&&i.assigned&&(y.assigned=i==null?void 0:i.assigned.toString()),i!=null&&i.ticket&&(y.ticket=i==null?void 0:i.ticket.toString()),i!=null&&i.priority&&(y.priority=i==null?void 0:i.priority)}const D=kt(t);D?y.parentId=D.id||"kbn"+V++:tt.push(y),N.push(y)},"addNode"),f={DEFAULT:0,NO_BORDER:0,ROUNDED_RECT:1,RECT:2,CIRCLE:3,CLOUD:4,BANG:5,HEXAGON:6},xt={clear:bt,addNode:Nt,getSections:ct,getData:St,nodeType:f,getType:a((t,u)=>{switch(q.debug("In get type",t,u),t){case"[":return f.RECT;case"(":return u===")"?f.ROUNDED_RECT:f.CLOUD;case"((":return f.CIRCLE;case")":return f.CLOUD;case"))":return f.BANG;case"{{":return f.HEXAGON;default:return f.DEFAULT}},"getType"),setElementForId:a((t,u)=>{Z[t]=u},"setElementForId"),decorateNode:a(t=>{if(!t)return;const u=H(),p=N[N.length-1];t.icon&&(p.icon=B(t.icon,u)),t.class&&(p.cssClasses=B(t.class,u))},"decorateNode"),type2Str:a(t=>{switch(t){case f.DEFAULT:return"no-border";case f.RECT:return"rect";case f.ROUNDED_RECT:return"rounded-rect";case f.CIRCLE:return"circle";case f.CLOUD:return"cloud";case f.BANG:return"bang";case f.HEXAGON:return"hexgon";default:return"no-border"}},"type2Str"),getLogger:a(()=>q,"getLogger"),getElementById:a(t=>Z[t],"getElementById")},Lt={draw:a(async(t,u,p,s)=>{var L,F,U,M,w;q.debug(`Rendering kanban diagram
`+t);const m=s.db.getData(),d=H();d.htmlLabels=!1;const g=gt(u),y=g.append("g");y.attr("class","sections");const D=g.append("g");D.attr("class","items");const v=m.nodes.filter(E=>E.isGroup);let x=0;const A=[];let i=25;for(const E of v){const I=((L=d==null?void 0:d.kanban)==null?void 0:L.sectionWidth)||200;x+=1,E.x=I*x+10*(x-1)/2,E.width=I,E.y=0,E.height=3*I,E.rx=5,E.ry=5,E.cssClasses=E.cssClasses+" section-"+x;const O=await pt(y,E);i=Math.max(i,(F=O==null?void 0:O.labelBBox)==null?void 0:F.height),A.push(O)}let G=0;for(const E of v){const I=A[G];G+=1;const O=((U=d==null?void 0:d.kanban)==null?void 0:U.sectionWidth)||200,$=3*-O/2+i;let e=$;const l=m.nodes.filter(h=>h.parentId===E.id);for(const h of l){if(h.isGroup)throw new Error("Groups within groups are not allowed in Kanban diagrams");h.x=E.x,h.width=O-15;const n=(await ut(D,h,{config:d})).node().getBBox();h.y=e+n.height/2,await dt(h),e=h.y+n.height/2+5}const o=I.cluster.select("rect"),r=Math.max(e-$+30,50)+(i-25);o.attr("height",r)}yt(void 0,g,((M=d.mindmap)==null?void 0:M.padding)??K.kanban.padding,((w=d.mindmap)==null?void 0:w.useMaxWidth)??K.kanban.useMaxWidth)},"draw")},Dt=a(t=>{let u="";for(let s=0;s<t.THEME_COLOR_LIMIT;s++)t["lineColor"+s]=t["lineColor"+s]||t["cScaleInv"+s],_t(t["lineColor"+s])?t["lineColor"+s]=ot(t["lineColor"+s],20):t["lineColor"+s]=at(t["lineColor"+s],20);const p=a((s,m)=>t.darkMode?at(s,m):ot(s,m),"adjuster");for(let s=0;s<t.THEME_COLOR_LIMIT;s++){const m=""+(17-3*s);u+=`
    .section-${s-1} rect, .section-${s-1} path, .section-${s-1} circle, .section-${s-1} polygon, .section-${s-1} path  {
      fill: ${p(t["cScale"+s],10)};
      stroke: ${p(t["cScale"+s],10)};

    }
    .section-${s-1} text {
     fill: ${t["cScaleLabel"+s]};
    }
    .node-icon-${s-1} {
      font-size: 40px;
      color: ${t["cScaleLabel"+s]};
    }
    .section-edge-${s-1}{
      stroke: ${t["cScale"+s]};
    }
    .edge-depth-${s-1}{
      stroke-width: ${m};
    }
    .section-${s-1} line {
      stroke: ${t["cScaleInv"+s]} ;
      stroke-width: 3;
    }

    .disabled, .disabled circle, .disabled text {
      fill: lightgray;
    }
    .disabled text {
      fill: #efefef;
    }

  .node rect,
  .node circle,
  .node ellipse,
  .node polygon,
  .node path {
    fill: ${t.background};
    stroke: ${t.nodeBorder};
    stroke-width: 1px;
  }

  .kanban-ticket-link {
    fill: ${t.background};
    stroke: ${t.nodeBorder};
    text-decoration: underline;
  }
    `}return u},"genSections"),ye={db:xt,renderer:Lt,parser:Et,styles:a(t=>`
  .edge {
    stroke-width: 3;
  }
  ${Dt(t)}
  .section-root rect, .section-root path, .section-root circle, .section-root polygon  {
    fill: ${t.git0};
  }
  .section-root text {
    fill: ${t.gitBranchLabel0};
  }
  .icon-container {
    height:100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .edge {
    fill: none;
  }
  .cluster-label, .label {
    color: ${t.textColor};
    fill: ${t.textColor};
    }
  .kanban-label {
    dy: 1em;
    alignment-baseline: middle;
    text-anchor: middle;
    dominant-baseline: middle;
    text-align: center;
  }
`,"getStyles")};export{ye as diagram};
