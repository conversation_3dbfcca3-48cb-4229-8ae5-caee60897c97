import{z as D,l as z,C as i,a6 as E,J as u,H as g,L as h,t as c,M as p,O as K,aa as P,b as r,N as m,T as Q,F as S,G as U,Z as y,a2 as C}from"./SpinnerAugment-C3d3R_8C.js";import{B as V,b as e}from"./IconButtonAugment-BYROpfM6.js";var W=u('<div class="c-button--icon svelte-14satks"><!></div>'),X=u('<div class="c-button--text svelte-14satks"><!></div>'),Y=u('<div class="c-button--icon svelte-14satks"><!></div>'),_=u("<div><!> <!> <!></div>");function et(x,t){const d=D(t),B=z(t,["children","$$slots","$$events","$$legacy"]),L=z(B,["size","variant","color","highContrast","disabled","radius","loading","alignment"]);let o=i(t,"size",8,2),$=i(t,"variant",8,"solid"),R=i(t,"color",8,"neutral"),F=i(t,"highContrast",8,!1),G=i(t,"disabled",8,!1),H=i(t,"radius",8,"medium"),J=i(t,"loading",8,!1),j=i(t,"alignment",8,"center");V(x,E({get size(){return o()},get variant(){return $()},get color(){return R()},get highContrast(){return F()},get disabled(){return G()},get loading(){return J()},get alignment(){return j()},get radius(){return H()}},()=>L,{$$events:{click(a){e.call(this,t,a)},keyup(a){e.call(this,t,a)},keydown(a){e.call(this,t,a)},mousedown(a){e.call(this,t,a)},mouseover(a){e.call(this,t,a)},focus(a){e.call(this,t,a)},mouseleave(a){e.call(this,t,a)},blur(a){e.call(this,t,a)},contextmenu(a){e.call(this,t,a)}},children:(a,w)=>{var v=_(),b=c(v),A=s=>{var l=W(),n=c(l);m(n,t,"iconLeft",{},null),r(s,l)};g(b,s=>{h(()=>d.iconLeft)&&s(A)});var f=p(b,2),I=s=>{var l=X(),n=c(l);const O=C(()=>o()===.5?1:o()),T=C(()=>$()==="ghost"?"regular":"medium");Q(n,{get size(){return y(O)},get weight(){return y(T)},children:(Z,tt)=>{var k=S(),q=U(k);m(q,t,"default",{},null),r(Z,k)},$$slots:{default:!0}}),r(s,l)};g(f,s=>{h(()=>d.default)&&s(I)});var M=p(f,2),N=s=>{var l=Y(),n=c(l);m(n,t,"iconRight",{},null),r(s,l)};g(M,s=>{h(()=>d.iconRight)&&s(N)}),K(()=>P(v,1,`c-button--content c-button--size-${o()}`,"svelte-14satks")),r(a,v)},$$slots:{default:!0}}))}export{et as B};
