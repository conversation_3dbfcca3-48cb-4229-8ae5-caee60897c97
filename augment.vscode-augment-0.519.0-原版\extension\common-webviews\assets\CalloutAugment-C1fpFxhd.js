import{z as N,l as C,A as T,C as a,W,X as m,m as q,Y as X,K as Y,Z as l,_ as j,D as S,J as u,a as Z,$ as _,a0 as k,T as w,G as B,H as E,L as F,M as O,N as b,t as r,b as v,I as P}from"./SpinnerAugment-C3d3R_8C.js";var Q=u('<div class="c-callout-icon svelte-1u5qnh6"><!></div>'),R=u('<!> <div class="c-callout-body svelte-1u5qnh6"><!></div>',1),U=u("<div><!></div>");function G(p,s){const y=N(s),x=C(s,["children","$$slots","$$events","$$legacy"]),t=C(x,["color","variant","size","highContrast"]);T(s,!1);const c=q(),o=q();let $=a(s,"color",8,"info"),A=a(s,"variant",8,"soft"),d=a(s,"size",8,2),D=a(s,"highContrast",8,!1);const H=d();W(()=>(l(c),l(o),Y(t)),()=>{m(c,t.class),m(o,X(t,["class"]))}),j(),S();var n=U();Z(n,(i,h)=>({...i,class:`c-callout c-callout--${$()} c-callout--${A()} c-callout--size-${d()} ${l(c)}`,...l(o),[k]:h}),[()=>_($()),()=>({"c-callout--highContrast":D()})],"svelte-1u5qnh6");var I=r(n);w(I,{get size(){return H},children:(i,h)=>{var f=R(),g=B(f),J=e=>{var z=Q(),M=r(z);b(M,s,"icon",{},null),v(e,z)};E(g,e=>{F(()=>y.icon)&&e(J)});var K=O(g,2),L=r(K);b(L,s,"default",{},null),v(i,f)},$$slots:{default:!0}}),v(p,n),P()}export{G as C};
