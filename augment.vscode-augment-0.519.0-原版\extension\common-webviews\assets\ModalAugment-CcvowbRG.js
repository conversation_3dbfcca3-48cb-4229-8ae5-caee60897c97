import{z as ea,A as sa,B as oa,C as l,D as la,F as g,G as z,H as i,b as t,I as ta,J as h,K as da,L as k,t as n,M as H,N as y,O as I,P as ra,Q as ca,R as q,T as ia,S as na,V as va}from"./SpinnerAugment-C3d3R_8C.js";import{C as fa,s as J}from"./CardAugment-L1_52yiK.js";import{b as K}from"./IconButtonAugment-BYROpfM6.js";var wa=h('<div class="c-modal-header svelte-1hwqfwo"><!></div>'),ha=h('<div class="c-modal-body svelte-1hwqfwo"><!></div>'),ma=h('<div class="c-modal-footer svelte-1hwqfwo"><!></div>'),pa=h('<div class="c-modal-content svelte-1hwqfwo"><!> <!> <!></div>'),ua=h('<div class="c-modal-backdrop svelte-1hwqfwo" role="presentation"><div class="c-modal svelte-1hwqfwo" role="dialog" aria-modal="true" tabindex="0"><!></div></div>');function xa(N,a){const v=ea(a);sa(a,!1);const m=oa();let O=l(a,"show",8,!1),p=l(a,"title",8,""),P=l(a,"maxWidth",8,"400px"),Q=l(a,"preventBackdropClose",8,!1),R=l(a,"preventEscapeClose",8,!1),B=l(a,"ariaLabelledBy",8,"modal-title"),D=l(a,"oncancel",24,()=>{}),S=l(a,"onbackdropClick",24,()=>{}),T=l(a,"onkeydown",24,()=>{});function W(){var e,o;Q()||(m("cancel"),(e=D())==null||e()),m("backdropClick"),(o=S())==null||o()}function j(e){var o,d;e.key!=="Escape"||R()||(e.preventDefault(),m("cancel"),(o=D())==null||o()),m("keydown",e),(d=T())==null||d(e)}la();var L=g(),V=z(L),U=e=>{var o=ua(),d=n(o),X=n(d);fa(X,{variant:"soft",size:3,children:(f,ba)=>{var M=pa(),A=n(M),Y=s=>{var r=wa(),w=n(r),C=c=>{var b=g(),x=z(b);y(x,a,"header",{},null),t(c,b)},u=(c,b)=>{var x=$=>{ia($,{get id(){return B()},size:3,weight:"bold",color:"primary",children:(aa,ka)=>{var G=na();I(()=>va(G,p())),t(aa,G)},$$slots:{default:!0}})};i(c,$=>{p()&&$(x)},b)};i(w,c=>{k(()=>v.header)?c(C):c(u,!1)}),t(s,r)};i(A,s=>{da(p()),k(()=>p()||v.header)&&s(Y)});var F=H(A,2),Z=s=>{var r=ha(),w=n(r);y(w,a,"body",{},C=>{var u=g(),c=z(u);y(c,a,"default",{},null),t(C,u)}),t(s,r)};i(F,s=>{k(()=>v.body||v.default)&&s(Z)});var _=H(F,2),E=s=>{var r=ma(),w=n(r);y(w,a,"footer",{},null),t(s,r)};i(_,s=>{k(()=>v.footer)&&s(E)}),t(f,M)},$$slots:{default:!0}}),I(()=>{ra(d,"aria-labelledby",B()),ca(d,`max-width: ${P()??""}`)}),q("click",d,J(function(f){K.call(this,a,f)})),q("keydown",d,J(function(f){K.call(this,a,f)})),q("click",o,W),q("keydown",o,j),t(e,o)};i(V,e=>{O()&&e(U)}),t(N,L),ta()}export{xa as M};
