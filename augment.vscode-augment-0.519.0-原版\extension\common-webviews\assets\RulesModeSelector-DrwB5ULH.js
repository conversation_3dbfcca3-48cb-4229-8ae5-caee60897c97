import{A as ee,C as k,W as U,X as v,m as h,K as B,Z as e,_ as ae,D as se,F as te,G as A,H as F,L as f,b as a,I as le,a1 as oe,S as y,J as E,O as W,V as q,t as re,M as z,a2 as ie,a3 as ne,a4 as de}from"./SpinnerAugment-C3d3R_8C.js";import{e as ce,i as ue}from"./IconButtonAugment-BYROpfM6.js";import{A as ve,D as d}from"./index-BWYp8-tu.js";import{B as G}from"./ButtonAugment-Cfj8nOxE.js";import{C as he}from"./chevron-down-CsTV-WoH.js";import{T as fe}from"./CardAugment-L1_52yiK.js";import{R}from"./message-broker-BVKpqRQ5.js";var pe=E('<div class="c-dropdown-label svelte-9n7h82"><!></div>'),me=E("<!> <!>",1),ge=E("<!> <!>",1),$e=E("<!> <!>",1);function Se(J,T){ee(T,!1);const[H,K]=oe(),p=()=>ne(e(C),"$focusedIndex",H),w=h(),S=h(),s=h();let O=k(T,"onSave",8),i=k(T,"rule",8);const x=[{label:"Always",value:R.ALWAYS_ATTACHED,description:"These Rules will be included in every message you send to the agent."},{label:"Manual",value:R.MANUAL,description:"These Rules will be included when manually tagged in your message. You can tag Rules by @-mentioning them."},{label:"Auto",value:R.AGENT_REQUESTED,description:"These Rules will be included when the Agent decides to fetch them based on this file's description."}];let C=h(void 0),I=h(()=>{});U(()=>B(i()),()=>{v(w,i().path)}),U(()=>B(i()),()=>{v(S,i().type)}),U(()=>e(S),()=>{v(s,x.find(t=>t.value===e(S)))}),ae(),se();var Q=te(),V=A(Q),X=t=>{fe(t,{content:"Workspace guidelines are always applied",children:(r,j)=>{G(r,{color:"accent",size:1,disabled:!0,children:(m,D)=>{var L=y("Always");a(m,L)},$$slots:{default:!0}})},$$slots:{default:!0}})},Z=t=>{d.Root(t,{get requestClose(){return e(I)},set requestClose(r){v(I,r)},get focusedIndex(){return e(C)},set focusedIndex(r){de(v(C,r),"$focusedIndex",H)},children:(r,j)=>{var m=$e(),D=A(m);d.Trigger(D,{children:(M,P)=>{var c=pe(),g=re(c);G(g,{color:"neutral",size:1,variant:"soft",children:(u,_)=>{var l=y();W(()=>q(l,(e(s),f(()=>e(s).label)))),a(u,l)},$$slots:{default:!0,iconRight:(u,_)=>{he(u,{slot:"iconRight"})}}}),a(M,c)},$$slots:{default:!0}});var L=z(D,2);d.Content(L,{side:"bottom",align:"start",children:(M,P)=>{var c=ge(),g=A(c);ce(g,1,()=>x,ue,(l,o)=>{const $=ie(()=>(e(s),e(o),f(()=>e(s).label===e(o).label)));d.Item(l,{onSelect:()=>async function(n){e(I)();try{await O()(n.value,n.value!==R.AGENT_REQUESTED||i().description?i().description:"Example description")}catch(b){console.error("RulesModeSelector: Error in onSave:",b)}}(e(o)),get highlight(){return e($)},children:(n,b)=>{var N=y();W(()=>q(N,(e(o),f(()=>e(o).label)))),a(n,N)},$$slots:{default:!0}})});var u=z(g,2),_=l=>{var o=me(),$=A(o);d.Separator($,{});var n=z($,2);d.Label(n,{children:(b,N)=>{var Y=y();W(()=>q(Y,(p(),e(s),f(()=>p()!==void 0?x[p()].description:e(s).description)))),a(b,Y)},$$slots:{default:!0}}),a(l,o)};F(u,l=>{(p()!==void 0||e(s))&&l(_)}),a(M,c)},$$slots:{default:!0}}),a(r,m)},$$slots:{default:!0},$$legacy:!0})};F(V,t=>{e(w),f(()=>e(w)===ve)?t(X):t(Z,!1)}),a(J,Q),le(),K()}export{Se as R};
