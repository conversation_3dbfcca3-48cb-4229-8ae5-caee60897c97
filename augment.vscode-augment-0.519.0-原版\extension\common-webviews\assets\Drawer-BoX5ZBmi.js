import{A as I,C as o,m as u,aj as ee,Z as e,X as i,W as X,K as w,_ as ae,D as ie,J as T,R as k,ak as J,Q as K,t as p,N as L,al as N,M as O,H as te,O as se,a2 as re,aa as Q,b as S,I as de}from"./SpinnerAugment-C3d3R_8C.js";import{a as ne,I as oe}from"./IconButtonAugment-BYROpfM6.js";import{t as le,f as ce}from"./index-B2GSXkDj.js";import{E as ve}from"./ellipsis-v5eG5a7L.js";const R=(_,{onResize:t,options:g})=>{const v=new ResizeObserver(t);return v.observe(_,g),{destroy(){v.unobserve(_),v.disconnect()}}};var me=T('<div class="c-drawer__hidden-indicator svelte-18f0m3o"><!></div>'),ue=T('<div><div class="c-drawer__left svelte-18f0m3o"><div class="c-drawer__left-content svelte-18f0m3o"><!></div></div> <div aria-hidden="true"></div> <div class="c-drawer__right svelte-18f0m3o"><!></div> <!></div>');function ge(_,t){I(t,!1);let g,v,j=o(t,"initialWidth",8,300),z=o(t,"expandedMinWidth",8,50),A=o(t,"minimizedWidth",8,0),s=o(t,"minimized",12,!1),Z=o(t,"class",8,""),q=o(t,"showButton",8,!0),F=o(t,"deadzone",8,0),G=o(t,"columnLayoutThreshold",8,600),d=o(t,"layoutMode",28,()=>{}),W=u(),f=u(),l=u(!1),c=u(j()),y=u(j()),r=u(!1);function B(){s(!s())}function M(){if(e(f)){if(d()!==void 0)return i(r,d()==="column"),void(e(r)&&i(l,!1));i(r,e(f).clientWidth<G()),e(r)&&i(l,!1)}}ee(M),X(()=>(w(s()),w(d())),()=>{s()?(d("row"),i(r,!1)):d()!=="row"||s()||(d(void 0),M())}),X(()=>(w(d()),e(r)),()=>{d()!==void 0&&(i(r,d()==="column"),e(r)&&i(l,!1))}),X(()=>(w(s()),w(A()),e(c)),()=>{i(y,s()?A():e(c))}),ae(),ie();var h=ue();let C;k("mousemove",J,function(a){if(!e(l)||!e(W)||e(r))return;const n=a.clientX-g,b=e(f).clientWidth-200,m=v+n;m<z()?m<z()-F()?s(!0):(i(c,z()),s(!1)):m>b?(i(c,b),s(!1)):(i(c,m),s(!1))}),k("mouseup",J,function(){i(l,!1),i(c,Math.max(e(c),z()))});var x=p(h),D=p(x);K(D,"",{},{width:"var(--augment-drawer-width)","min-width":"var(--augment-drawer-width)","max-width":"var(--augment-drawer-width)"});var P=p(D);L(P,t,"left",{},null),N(x,a=>i(W,a),()=>e(W));var $=O(x,2);let E;var H=O($,2),U=p(H);L(U,t,"right",{},null);var V=O(H,2),Y=a=>{var n=me(),b=p(n);oe(b,{variant:"solid",color:"accent",size:2,radius:"full",title:"Show panel",$$events:{click:B},children:(m,fe)=>{ve(m,{})},$$slots:{default:!0}}),le(3,n,()=>ce,()=>({y:0,x:0,duration:200})),S(a,n)};te(V,a=>{s()&&q()&&a(Y)}),N(h,a=>i(f,a),()=>e(f)),ne(h,(a,n)=>R==null?void 0:R(a,n),()=>({onResize:()=>d()===void 0&&M()})),se((a,n)=>{C=Q(h,1,`c-drawer ${Z()??""}`,"svelte-18f0m3o",C,a),K(x,`--augment-drawer-width:${e(y)??""}px;`),D.inert=e(l),E=Q($,1,"c-drawer__handle svelte-18f0m3o",null,E,n)},[()=>({"is-dragging":e(l),"is-hidden":!e(y),"is-column":e(r)}),()=>({"is-locked":e(r)})],re),k("mousedown",$,function(a){e(r)||(i(l,!0),g=a.clientX,v=e(W).offsetWidth,a.preventDefault())}),k("dblclick",$,B),S(_,h),de()}export{ge as D,R as r};
