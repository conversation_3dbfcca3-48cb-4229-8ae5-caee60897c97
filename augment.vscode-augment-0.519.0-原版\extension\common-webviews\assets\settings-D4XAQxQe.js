var Ca=Object.defineProperty;var wa=(r,e,t)=>e in r?Ca(r,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):r[e]=t;var ze=(r,e,t)=>wa(r,typeof e!="symbol"?e+"":e,t);import{f as Ct,b as d,w as nt,aD as Rr,aA as vr,A as Be,C,D as We,J as v,H as K,Z as n,L as f,t as h,M as p,a2 as Se,O as we,aa as _t,V as Ie,aF as ls,T as ve,S as Z,R as ut,I as Je,W as Me,X as g,m as X,K as _,_ as it,P as Kt,Q as ba,F as tt,G as $e,a3 as Xe,a1 as Mt,z as $a,l as us,Y as Sa,a as Fs,a0 as ka,N as yt,ak as rr,a6 as xa,b4 as Er,ay as mr,v as _s,u as ds,b5 as Ir,aE as or,a4 as Js,B as xr,aj as Kr,a$ as zr,a5 as Yr,b6 as Ma,az as Aa}from"./SpinnerAugment-C3d3R_8C.js";import"./design-system-init-s3QUMN0e.js";import{W as De,d as ft,e as at,I as Qt,b as Ta,c as et,i as Dt,a as Na,h as ar,f as Or,g as Ra,H as lr}from"./IconButtonAugment-BYROpfM6.js";import{M as Xr,R as Lr}from"./message-broker-BVKpqRQ5.js";import{G as Ea,S as Ia,b as za,N as Oa,L as La,c as lt,M as ns,D as Za,F as Pa,f as Qr,R as Fa,d as Zr,T as ea,e as ja,C as Da,P as dr,g as Ua,h as Va,i as qa,A as Ha,j as Ba,k as Ja,U as Pr}from"./user-BS1qxHV9.js";import{Q as cr,a2 as zt,O as je,i as ur,t as gr,T as Yt,D as qe,a3 as Ga,C as ta,E as sa,a4 as Vs,f as hr,A as Fr,g as Wa,h as Ka,R as Ya}from"./index-BWYp8-tu.js";import{G as Xa,D as qs,C as Qa,P as hs,B as Mr,a as en,T as fr,b as ra,S as yr}from"./download-DEUSNPpd.js";import{o as Bs}from"./keypress-DD1aQVr0.js";import{V as aa}from"./VSCodeCodicon-Cl6-aUcf.js";import{A as tn}from"./async-messaging-BeBg25ZO.js";import{c as _r}from"./svelte-component-ClwSdZAs.js";import{k as sn,C as na,a as rn,T as Cr}from"./CollapseButtonAugment-ClJpCixO.js";import{D as an}from"./Drawer-BoX5ZBmi.js";import{b as ia,T as Lt,a as ps,p as nn}from"./CardAugment-L1_52yiK.js";import{B as He}from"./ButtonAugment-Cfj8nOxE.js";import{C as Gs}from"./CalloutAugment-C1fpFxhd.js";import{E as on}from"./ellipsis-v5eG5a7L.js";import{P as ln}from"./pen-to-square-DGRIEioi.js";import{T as oa,S as dn}from"./TextAreaAugment-BGi_2d0z.js";import{C as Ar}from"./chevron-down-CsTV-WoH.js";import{M as cn}from"./index-BPBpJFCC.js";import{M as un}from"./MarkdownEditor-B2OO4mz6.js";import{R as hn}from"./RulesModeSelector-DrwB5ULH.js";import{M as la}from"./ModalAugment-CcvowbRG.js";import"./types-CGlLNakm.js";import"./file-paths-BEJIrsZp.js";import"./isObjectLike-DEzymPim.js";import"./BaseTextInput-hjj6nBZd.js";import"./index-B2GSXkDj.js";import"./index-BiRO5qTg.js";const Cs={maxMS:9e5,initialMS:6e4,mult:2,maxSteps:4};class pn{constructor(e,t=Cs){ze(this,"timerId",null);ze(this,"currentMS");ze(this,"step",0);ze(this,"params");this.callback=e;const s={...t};s.maxMS<0&&(console.warn("PollingManager: Negative maxMS detected, using default value of 15 minutes"),s.maxMS=Cs.maxMS),s.initialMS<=0&&(console.warn("PollingManager: Negative or zero initialMS detected, using default value of 1 minute"),s.initialMS=Cs.initialMS),s.mult<=0&&(console.warn("PollingManager: Negative or zero multiplier detected, using default value of 2"),s.mult=Cs.mult),s.maxSteps!==void 0&&s.maxSteps<0&&(console.warn("PollingManager: Negative maxSteps detected, using default value of 4"),s.maxSteps=Cs.maxSteps),this.params=s,this.currentMS=this.params.maxMS}startPolling(){this.stopPolling(),this.currentMS=this.params.initialMS,this.step=0,this.safeExecute(),this.scheduleNext()}stopPolling(){this.timerId!==null&&(window.clearTimeout(this.timerId),this.timerId=null)}dispose(){this.stopPolling()}scheduleNext(){this.timerId=window.setTimeout(()=>{if(this.safeExecute(),this.params.maxMS===0){if(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps)return void this.stopPolling()}else this.currentMS<this.params.maxMS&&(this.step++,this.params.maxSteps!==void 0&&this.step>=this.params.maxSteps?(this.currentMS=this.params.maxMS,this.step=0):this.currentMS=Math.min(this.currentMS*this.params.mult,this.params.maxMS));this.scheduleNext()},this.currentMS)}safeExecute(){try{const e=this.callback();e instanceof Promise&&e.catch(t=>console.error("Error in polling callback:",t))}catch(e){console.error("Error in polling callback:",e)}}}var vn=Ct('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.43703 10.7785C2.30998 10.978 2.16478 11.2137 2.05588 11.3951C1.94698 11.5764 2.00143 11.8121 2.18293 11.921L4.66948 13.4442C4.85098 13.553 5.08695 13.4986 5.19585 13.3173C5.2866 13.1541 5.41365 12.9365 5.55885 12.7007C6.53895 11.0868 7.5372 11.2681 9.3159 12.1204L11.7843 13.281C11.9839 13.3717 12.2017 13.281 12.2925 13.0997L13.4722 10.4339C13.563 10.2526 13.4722 10.0169 13.2907 9.92619C12.7644 9.69044 11.7298 9.20084 10.8223 8.74749C7.44645 7.13354 4.59689 7.24234 2.43703 10.7785Z" fill="currentColor"></path><path d="M13.563 4.72157C13.69 4.52209 13.8352 4.28635 13.9441 4.105C14.053 3.92366 13.9985 3.68791 13.817 3.57911L11.3305 2.05583C11.149 1.94702 10.913 2.00143 10.8041 2.18277C10.7134 2.34598 10.5863 2.56359 10.4411 2.79934C9.461 4.41329 8.46275 4.23194 6.68405 3.37963L4.21563 2.21904C4.01598 2.12837 3.79818 2.21904 3.70743 2.40038L2.52767 5.0661C2.43692 5.24745 2.52767 5.4832 2.70917 5.5739C3.23552 5.80965 4.27007 6.29925 5.1776 6.7526C8.53535 8.34845 11.3849 8.25775 13.563 4.72157Z" fill="currentColor"></path></svg>');function mn(r){var e=vn();d(r,e)}var gn=Ct('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M13.5028 2H7.7257C7.7257 3.44 8.8914 4.60571 10.3314 4.60571H11.3942V5.6343C11.3942 7.0743 12.5599 8.24 14 8.24V2.49714C14 2.22285 13.7771 2 13.5028 2ZM10.6399 4.88H4.86279C4.86279 6.32 6.0285 7.4857 7.4685 7.4857H8.53135V8.5143C8.53135 9.9543 9.69705 11.12 11.137 11.12V5.37715C11.137 5.10285 10.9142 4.88 10.6399 4.88ZM2 7.75995H7.7771C8.0514 7.75995 8.27425 7.9828 8.27425 8.2571V13.9999C6.83425 13.9999 5.66855 12.8342 5.66855 11.3942V10.3656H4.6057C3.16571 10.3656 2 9.19995 2 7.75995Z" fill="currentColor"></path></svg>');function fn(r){var e=gn();d(r,e)}var yn=Ct('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3 2.5C3 2.22386 3.22386 2 3.5 2H9.08579C9.21839 2 9.34557 2.05268 9.43934 2.14645L11.8536 4.56066C11.9473 4.65443 12 4.78161 12 4.91421V12.5C12 12.7761 11.7761 13 11.5 13H3.5C3.22386 13 3 12.7761 3 12.5V2.5ZM3.5 1C2.67157 1 2 1.67157 2 2.5V12.5C2 13.3284 2.67157 14 3.5 14H11.5C12.3284 14 13 13.3284 13 12.5V4.91421C13 4.51639 12.842 4.13486 12.5607 3.85355L10.1464 1.43934C9.86514 1.15804 9.48361 1 9.08579 1H3.5ZM4.5 4C4.22386 4 4 4.22386 4 4.5C4 4.77614 4.22386 5 4.5 5H7.5C7.77614 5 8 4.77614 8 4.5C8 4.22386 7.77614 4 7.5 4H4.5ZM4.5 7C4.22386 7 4 7.22386 4 7.5C4 7.77614 4.22386 8 4.5 8H10.5C10.7761 8 11 7.77614 11 7.5C11 7.22386 10.7761 7 10.5 7H4.5ZM4.5 10C4.22386 10 4 10.2239 4 10.5C4 10.7761 4.22386 11 4.5 11H10.5C10.7761 11 11 10.7761 11 10.5C11 10.2239 10.7761 10 10.5 10H4.5Z" fill="currentColor" fill-rule="evenodd" clip-rule="evenodd"></path></svg>');function Ft(r){var e=yn();d(r,e)}var Ze,wr,_n=Ct('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 2C3.22386 2 3 2.22386 3 2.5V12.5C3 12.7761 3.22386 13 3.5 13H11.5C11.7761 13 12 12.7761 12 12.5V4.70711L9.29289 2H3.5ZM2 2.5C2 1.67157 2.67157 1 3.5 1H9.5C9.63261 1 9.75979 1.05268 9.85355 1.14645L12.7803 4.07322C12.921 4.21388 13 4.40464 13 4.60355V12.5C13 13.3284 12.3284 14 11.5 14H3.5C2.67157 14 2 13.3284 2 12.5V2.5ZM4.75 7.5C4.75 7.22386 4.97386 7 5.25 7H9.75C10.0261 7 10.25 7.22386 10.25 7.5C10.25 7.77614 10.0261 8 9.75 8H5.25C4.97386 8 4.75 7.77614 4.75 7.5Z" fill="currentColor"></path></svg>');function Cn(r){var e=_n();d(r,e)}class ks{constructor(e){ze(this,"configs",nt([]));ze(this,"pollingManager");ze(this,"_enableDebugFeatures",nt(!1));ze(this,"_settingsComponentSupported",nt({workspaceContext:!1,mcpServerList:!1,mcpServerImport:!1,orientation:!1,remoteTools:!1,userGuidelines:!1,terminal:!1,rules:!1}));ze(this,"_enableAgentMode",nt(!1));ze(this,"_enableAgentSwarmMode",nt(!1));ze(this,"_enableNativeRemoteMcp",nt(!0));ze(this,"_hasEverUsedRemoteAgent",nt(!1));ze(this,"_enableInitialOrientation",nt(!1));ze(this,"_userTier",nt("unknown"));ze(this,"_guidelines",nt({}));this._host=e,this.pollingManager=new pn(()=>this.requestToolStatus(!1),{maxMS:0,initialMS:2e3,mult:1,maxSteps:150}),this.requestToolStatus(!1)}transformToolDisplay(e){const t=!e.isConfigured,s=e.oauthUrl;if(e.identifier.hostName===cr.remoteToolHost){let a=e.identifier.toolId;switch(typeof a=="string"&&/^\d+$/.test(a)&&(a=Number(a)),a){case zt.GitHubApi:return{displayName:"GitHub",description:"Configure GitHub API access for repository operations",icon:Xa,requiresAuthentication:t,authUrl:s};case zt.Linear:return{displayName:"Linear",description:"Configure Linear API access for issue tracking",icon:La,requiresAuthentication:t,authUrl:s};case zt.Jira:return{displayName:"Jira",description:"Configure Jira API access for issue tracking",icon:fn,requiresAuthentication:t,authUrl:s};case zt.Notion:return{displayName:"Notion",description:"Configure Notion API access",icon:Oa,requiresAuthentication:t,authUrl:s};case zt.Confluence:return{displayName:"Confluence",description:"Configure Confluence API access",icon:mn,requiresAuthentication:t,authUrl:s};case zt.WebSearch:return{displayName:"Web Search",description:"Configure web search capabilities",icon:za,requiresAuthentication:t,authUrl:s};case zt.Supabase:return{displayName:"Supabase",description:"Configure Supabase API access",icon:Ia,requiresAuthentication:t,authUrl:s};case zt.Glean:return{displayName:"Glean",description:"Configure Glean API access",icon:Ea,requiresAuthentication:t,authUrl:s};case zt.Unknown:return{displayName:"Unknown",description:"Unknown tool",requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled RemoteToolId: ${a}`)}}else if(e.identifier.hostName===cr.localToolHost){const a=e.identifier.toolId;switch(a){case je.readFile:case je.editFile:case je.saveFile:case je.launchProcess:case je.killProcess:case je.readProcess:case je.writeProcess:case je.listProcesses:case je.waitProcess:case je.openBrowser:case je.clarify:case je.onboardingSubAgent:case je.strReplaceEditor:case je.remember:case je.diagnostics:case je.setupScript:case je.readTerminal:case je.gitCommitRetrieval:case je.memoryRetrieval:case je.startWorkerAgent:case je.readWorkerState:case je.waitForWorkerAgent:case je.sendInstructionToWorkerAgent:case je.stopWorkerAgent:case je.deleteWorkerAgent:case je.readWorkerAgentEdits:case je.applyWorkerAgentEdits:case je.LocalSubAgent:return{displayName:e.definition.name.toString(),description:"Local tool",icon:Ft,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled LocalToolType: ${a}`)}}else if(e.identifier.hostName===cr.sidecarToolHost){const a=e.identifier.toolId;switch(a){case lt.codebaseRetrieval:return{displayName:"Code Search",description:"Configure codebase search capabilities",icon:ns,requiresAuthentication:t,authUrl:s};case lt.shell:return{displayName:"Shell",description:"Shell",icon:ns,requiresAuthentication:t,authUrl:s};case lt.strReplaceEditor:return{displayName:"File Edit",description:"File Editor",icon:ns,requiresAuthentication:t,authUrl:s};case lt.view:return{displayName:"File View",description:"File Viewer",icon:ns,requiresAuthentication:t,authUrl:s};case lt.webFetch:return{displayName:"Web Fetch",description:"Retrieve information from the web",icon:ns,requiresAuthentication:t,authUrl:s};case lt.removeFiles:return{displayName:"Remove Files",description:"Remove files from the codebase",icon:Cn,requiresAuthentication:t,authUrl:s};case lt.remember:return{displayName:e.definition.name.toString(),description:"Remember",icon:Ft,requiresAuthentication:t,authUrl:s};case lt.saveFile:return{displayName:"Save File",description:"Save a new file",icon:Pa,requiresAuthentication:t,authUrl:s};case lt.viewTaskList:return{displayName:"View Task List",description:"View the current task list",icon:Ft,requiresAuthentication:t,authUrl:s};case lt.reorganizeTaskList:return{displayName:"Reorganize Task List",description:"Reorganize the task list structure for major restructuring",icon:Ft,requiresAuthentication:t,authUrl:s};case lt.viewRangeUntruncated:return{displayName:e.definition.name.toString(),description:"View Range",icon:Ft,requiresAuthentication:t,authUrl:s};case lt.updateTasks:return{displayName:"Update Tasks",description:"Update one or more tasks in the task list",icon:Ft,requiresAuthentication:t,authUrl:s};case lt.addTasks:return{displayName:"Add Tasks",description:"Add one or more new tasks to the task list",icon:Ft,requiresAuthentication:t,authUrl:s};case lt.searchUntruncated:return{displayName:e.definition.name.toString(),description:"Search Untruncated",icon:Ft,requiresAuthentication:t,authUrl:s};case lt.renderMermaid:return{displayName:"View Mermaid Diagram",description:"View a mermaid diagram",icon:Za,requiresAuthentication:t,authUrl:s};case lt.grepSearch:return{displayName:"Grep search",description:"Run grep search",icon:ns,requiresAuthentication:t,authUrl:s};default:throw new Error(`Unhandled SidecarToolType: ${a}`)}}return{displayName:e.definition.name.toString(),description:e.definition.description||"",requiresAuthentication:t,authUrl:s}}handleMessageFromExtension(e){const t=e.data;switch(t.type){case De.toolConfigInitialize:return this.createConfigsFromHostTools(t.data.hostTools,t.data.toolConfigs),t.data&&t.data.enableDebugFeatures!==void 0&&this._enableDebugFeatures.set(t.data.enableDebugFeatures),t.data&&t.data.settingsComponentSupported!==void 0&&this._settingsComponentSupported.set(t.data.settingsComponentSupported),t.data.enableAgentMode!==void 0&&this._enableAgentMode.set(t.data.enableAgentMode),t.data.enableAgentSwarmMode!==void 0&&this._enableAgentSwarmMode.set(t.data.enableAgentSwarmMode),t.data.hasEverUsedRemoteAgent!==void 0&&this._hasEverUsedRemoteAgent.set(t.data.hasEverUsedRemoteAgent),t.data.enableInitialOrientation!==void 0&&this._enableInitialOrientation.set(t.data.enableInitialOrientation),t.data.userTier!==void 0&&this._userTier.set(t.data.userTier),t.data.guidelines!==void 0&&this._guidelines.set(t.data.guidelines),t.data.enableNativeRemoteMcp!==void 0&&this._enableNativeRemoteMcp.set(t.data.enableNativeRemoteMcp),!0;case De.toolConfigDefinitionsResponse:return this.configs.update(s=>this.createConfigsFromHostTools(t.data.hostTools,[]).map(a=>{const l=s.find(i=>i.name===a.name);return l?{...l,displayName:a.displayName,description:a.description,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,isConfigured:a.isConfigured,toolApprovalConfig:a.toolApprovalConfig}:a})),!0}return!1}createConfigsFromHostTools(e,t){return e.map(s=>{const a=this.transformToolDisplay(s),l=t.find(o=>o.name===s.definition.name),i=(l==null?void 0:l.isConfigured)??!a.requiresAuthentication;return{config:(l==null?void 0:l.config)??{},configString:JSON.stringify((l==null?void 0:l.config)??{},null,2),isConfigured:i,name:s.definition.name.toString(),displayName:a.displayName,description:a.description,identifier:s.identifier,icon:a.icon,requiresAuthentication:a.requiresAuthentication,authUrl:a.authUrl,showStatus:!1,statusMessage:"",statusType:"info",toolApprovalConfig:s.toolApprovalConfig}})}getConfigs(){return this.configs}isDisplayableTool(e){return["github","linear","notion","jira","confluence","supabase"].includes(e.displayName.toLowerCase())}getDisplayableTools(){return Rr(this.configs,e=>{const t=e.filter(a=>this.isDisplayableTool(a)),s=new Map;for(const a of t)s.set(a.displayName,a);return Array.from(s.values()).sort((a,l)=>{const i={GitHub:1,Linear:2,Notion:3},o=Number.MAX_SAFE_INTEGER,c=i[a.displayName]||o,u=i[l.displayName]||o;return c<o&&u<o||c===o&&u===o?c!==u?c-u:a.displayName.localeCompare(l.displayName):c-u})})}getPretendNativeToolDefs(){return Rr(this.configs,e=>this.getEnableNativeRemoteMcp()?Qr(e):[])}saveConfig(e){this.startPolling()}notifyLoaded(){this._host.postMessage({type:De.toolConfigLoaded})}startPolling(){this.pollingManager.startPolling()}requestToolStatus(e=!0){this._host.postMessage({type:De.toolConfigGetDefinitions,data:{useCache:e}})}dispose(){this.pollingManager.dispose()}getEnableDebugFeatures(){return this._enableDebugFeatures}getEnableAgentMode(){return this._enableAgentMode}getEnableAgentSwarmMode(){return this._enableAgentSwarmMode}getEnableNativeRemoteMcp(){return this._host.clientType==="vscode"&&this._enableNativeRemoteMcp}getHasEverUsedRemoteAgent(){return this._hasEverUsedRemoteAgent}getEnableInitialOrientation(){return this._enableInitialOrientation}getUserTier(){return this._userTier}getGuidelines(){return this._guidelines}updateLocalUserGuidelines(e){this._guidelines.update(t=>t.userGuidelines?{...t,userGuidelines:{...t.userGuidelines,contents:e,enabled:e.length>0}}:t)}updateToolApprovalConfig(e,t){this.configs.update(s=>s.map(a=>a.identifier.toolId===e.toolId&&a.identifier.hostName===e.hostName?{...a,toolApprovalConfig:t}:a))}getSettingsComponentSupported(){return this._settingsComponentSupported}}ze(ks,"key","toolConfigModel");(function(r){r.assertEqual=e=>e,r.assertIs=function(e){},r.assertNever=function(e){throw new Error},r.arrayToEnum=e=>{const t={};for(const s of e)t[s]=s;return t},r.getValidEnumValues=e=>{const t=r.objectKeys(e).filter(a=>typeof e[e[a]]!="number"),s={};for(const a of t)s[a]=e[a];return r.objectValues(s)},r.objectValues=e=>r.objectKeys(e).map(function(t){return e[t]}),r.objectKeys=typeof Object.keys=="function"?e=>Object.keys(e):e=>{const t=[];for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.push(s);return t},r.find=(e,t)=>{for(const s of e)if(t(s))return s},r.isInteger=typeof Number.isInteger=="function"?e=>Number.isInteger(e):e=>typeof e=="number"&&isFinite(e)&&Math.floor(e)===e,r.joinValues=function(e,t=" | "){return e.map(s=>typeof s=="string"?`'${s}'`:s).join(t)},r.jsonStringifyReplacer=(e,t)=>typeof t=="bigint"?t.toString():t})(Ze||(Ze={})),function(r){r.mergeShapes=(e,t)=>({...e,...t})}(wr||(wr={}));const W=Ze.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Ot=r=>{switch(typeof r){case"undefined":return W.undefined;case"string":return W.string;case"number":return isNaN(r)?W.nan:W.number;case"boolean":return W.boolean;case"function":return W.function;case"bigint":return W.bigint;case"symbol":return W.symbol;case"object":return Array.isArray(r)?W.array:r===null?W.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?W.promise:typeof Map<"u"&&r instanceof Map?W.map:typeof Set<"u"&&r instanceof Set?W.set:typeof Date<"u"&&r instanceof Date?W.date:W.object;default:return W.unknown}},$=Ze.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class gt extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(l){return l.message},s={_errors:[]},a=l=>{for(const i of l.issues)if(i.code==="invalid_union")i.unionErrors.map(a);else if(i.code==="invalid_return_type")a(i.returnTypeError);else if(i.code==="invalid_arguments")a(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let o=s,c=0;for(;c<i.path.length;){const u=i.path[c];c===i.path.length-1?(o[u]=o[u]||{_errors:[]},o[u]._errors.push(t(i))):o[u]=o[u]||{_errors:[]},o=o[u],c++}}};return a(this),s}static assert(e){if(!(e instanceof gt))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,Ze.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){const t={},s=[];for(const a of this.issues)a.path.length>0?(t[a.path[0]]=t[a.path[0]]||[],t[a.path[0]].push(e(a))):s.push(e(a));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}}gt.create=r=>new gt(r);const vs=(r,e)=>{let t;switch(r.code){case $.invalid_type:t=r.received===W.undefined?"Required":`Expected ${r.expected}, received ${r.received}`;break;case $.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,Ze.jsonStringifyReplacer)}`;break;case $.unrecognized_keys:t=`Unrecognized key(s) in object: ${Ze.joinValues(r.keys,", ")}`;break;case $.invalid_union:t="Invalid input";break;case $.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${Ze.joinValues(r.options)}`;break;case $.invalid_enum_value:t=`Invalid enum value. Expected ${Ze.joinValues(r.options)}, received '${r.received}'`;break;case $.invalid_arguments:t="Invalid function arguments";break;case $.invalid_return_type:t="Invalid function return type";break;case $.invalid_date:t="Invalid date";break;case $.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:Ze.assertNever(r.validation):t=r.validation!=="regex"?`Invalid ${r.validation}`:"Invalid";break;case $.too_small:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:"Invalid input";break;case $.too_big:t=r.type==="array"?`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:"Invalid input";break;case $.custom:t="Invalid input";break;case $.invalid_intersection_types:t="Intersection results could not be merged";break;case $.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case $.not_finite:t="Number must be finite";break;default:t=e.defaultError,Ze.assertNever(r)}return{message:t}};let da=vs;function Ws(){return da}const Ks=r=>{const{data:e,path:t,errorMaps:s,issueData:a}=r,l=[...t,...a.path||[]],i={...a,path:l};if(a.message!==void 0)return{...a,path:l,message:a.message};let o="";const c=s.filter(u=>!!u).slice().reverse();for(const u of c)o=u(i,{data:e,defaultError:o}).message;return{...a,path:l,message:o}};function V(r,e){const t=Ws(),s=Ks({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===vs?void 0:vs].filter(a=>!!a)});r.common.issues.push(s)}class ct{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){const s=[];for(const a of t){if(a.status==="aborted")return _e;a.status==="dirty"&&e.dirty(),s.push(a.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){const s=[];for(const a of t){const l=await a.key,i=await a.value;s.push({key:l,value:i})}return ct.mergeObjectSync(e,s)}static mergeObjectSync(e,t){const s={};for(const a of t){const{key:l,value:i}=a;if(l.status==="aborted"||i.status==="aborted")return _e;l.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),l.value==="__proto__"||i.value===void 0&&!a.alwaysSet||(s[l.value]=i.value)}return{status:e.value,value:s}}}const _e=Object.freeze({status:"aborted"}),Ys=r=>({status:"dirty",value:r}),ht=r=>({status:"valid",value:r}),br=r=>r.status==="aborted",$r=r=>r.status==="dirty",es=r=>r.status==="valid",xs=r=>typeof Promise<"u"&&r instanceof Promise;function Xs(r,e,t,s){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return e.get(r)}function ca(r,e,t,s,a){if(typeof e=="function"||!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return e.set(r,t),t}var ie,bs,$s;typeof SuppressedError=="function"&&SuppressedError,function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e==null?void 0:e.message}(ie||(ie={}));class Nt{constructor(e,t,s,a){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=a}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const jr=(r,e)=>{if(es(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new gt(r.common.issues);return this._error=t,this._error}}};function Ae(r){if(!r)return{};const{errorMap:e,invalid_type_error:t,required_error:s,description:a}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:a}:{errorMap:(l,i)=>{var o,c;const{message:u}=r;return l.code==="invalid_enum_value"?{message:u??i.defaultError}:i.data===void 0?{message:(o=u??s)!==null&&o!==void 0?o:i.defaultError}:l.code!=="invalid_type"?{message:i.defaultError}:{message:(c=u??t)!==null&&c!==void 0?c:i.defaultError}},description:a}}class Te{get description(){return this._def.description}_getType(e){return Ot(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Ot(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new ct,ctx:{common:e.parent.common,data:e.data,parsedType:Ot(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(xs(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;const a={common:{issues:[],async:(s=t==null?void 0:t.async)!==null&&s!==void 0&&s,contextualErrorMap:t==null?void 0:t.errorMap},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ot(e)},l=this._parseSync({data:e,path:a.path,parent:a});return jr(a,l)}"~validate"(e){var t,s;const a={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ot(e)};if(!this["~standard"].async)try{const l=this._parseSync({data:e,path:[],parent:a});return es(l)?{value:l.value}:{issues:a.common.issues}}catch(l){!((s=(t=l==null?void 0:l.message)===null||t===void 0?void 0:t.toLowerCase())===null||s===void 0)&&s.includes("encountered")&&(this["~standard"].async=!0),a.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:a}).then(l=>es(l)?{value:l.value}:{issues:a.common.issues})}async parseAsync(e,t){const s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){const s={common:{issues:[],contextualErrorMap:t==null?void 0:t.errorMap,async:!0},path:(t==null?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Ot(e)},a=this._parse({data:e,path:s.path,parent:s}),l=await(xs(a)?a:Promise.resolve(a));return jr(s,l)}refine(e,t){const s=a=>typeof t=="string"||t===void 0?{message:t}:typeof t=="function"?t(a):t;return this._refinement((a,l)=>{const i=e(a),o=()=>l.addIssue({code:$.custom,...s(a)});return typeof Promise<"u"&&i instanceof Promise?i.then(c=>!!c||(o(),!1)):!!i||(o(),!1)})}refinement(e,t){return this._refinement((s,a)=>!!e(s)||(a.addIssue(typeof t=="function"?t(s,a):t),!1))}_refinement(e){return new St({schema:this,typeName:fe.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:t=>this["~validate"](t)}}optional(){return At.create(this,this._def)}nullable(){return Bt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return kt.create(this)}promise(){return gs.create(this,this._def)}or(e){return Ns.create([this,e],this._def)}and(e){return Rs.create(this,e,this._def)}transform(e){return new St({...Ae(this._def),schema:this,typeName:fe.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t=typeof e=="function"?e:()=>e;return new Os({...Ae(this._def),innerType:this,defaultValue:t,typeName:fe.ZodDefault})}brand(){return new Tr({typeName:fe.ZodBranded,type:this,...Ae(this._def)})}catch(e){const t=typeof e=="function"?e:()=>e;return new Ls({...Ae(this._def),innerType:this,catchValue:t,typeName:fe.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return js.create(this,e)}readonly(){return Zs.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const wn=/^c[^\s-]{8,}$/i,bn=/^[0-9a-z]+$/,$n=/^[0-9A-HJKMNP-TV-Z]{26}$/i,Sn=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,kn=/^[a-z0-9_-]{21}$/i,xn=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Mn=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,An=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let pr;const Tn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Nn=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Rn=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,En=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,In=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,zn=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,ua="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",On=new RegExp(`^${ua}$`);function ha(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function pa(r){let e=`${ua}T${ha(r)}`;const t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function Ln(r,e){if(!xn.test(r))return!1;try{const[t]=r.split("."),s=t.replace(/-/g,"+").replace(/_/g,"/").padEnd(t.length+(4-t.length%4)%4,"="),a=JSON.parse(atob(s));return typeof a=="object"&&a!==null&&!(!a.typ||!a.alg)&&(!e||a.alg===e)}catch{return!1}}function Zn(r,e){return!(e!=="v4"&&e||!Nn.test(r))||!(e!=="v6"&&e||!En.test(r))}class $t extends Te{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==W.string){const i=this._getOrReturnCtx(e);return V(i,{code:$.invalid_type,expected:W.string,received:i.parsedType}),_e}const t=new ct;let s;for(const i of this._def.checks)if(i.kind==="min")e.data.length<i.value&&(s=this._getOrReturnCtx(e,s),V(s,{code:$.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="max")e.data.length>i.value&&(s=this._getOrReturnCtx(e,s),V(s,{code:$.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!1,message:i.message}),t.dirty());else if(i.kind==="length"){const o=e.data.length>i.value,c=e.data.length<i.value;(o||c)&&(s=this._getOrReturnCtx(e,s),o?V(s,{code:$.too_big,maximum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}):c&&V(s,{code:$.too_small,minimum:i.value,type:"string",inclusive:!0,exact:!0,message:i.message}),t.dirty())}else if(i.kind==="email")An.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"email",code:$.invalid_string,message:i.message}),t.dirty());else if(i.kind==="emoji")pr||(pr=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),pr.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"emoji",code:$.invalid_string,message:i.message}),t.dirty());else if(i.kind==="uuid")Sn.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"uuid",code:$.invalid_string,message:i.message}),t.dirty());else if(i.kind==="nanoid")kn.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"nanoid",code:$.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid")wn.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"cuid",code:$.invalid_string,message:i.message}),t.dirty());else if(i.kind==="cuid2")bn.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"cuid2",code:$.invalid_string,message:i.message}),t.dirty());else if(i.kind==="ulid")$n.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"ulid",code:$.invalid_string,message:i.message}),t.dirty());else if(i.kind==="url")try{new URL(e.data)}catch{s=this._getOrReturnCtx(e,s),V(s,{validation:"url",code:$.invalid_string,message:i.message}),t.dirty()}else i.kind==="regex"?(i.regex.lastIndex=0,i.regex.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"regex",code:$.invalid_string,message:i.message}),t.dirty())):i.kind==="trim"?e.data=e.data.trim():i.kind==="includes"?e.data.includes(i.value,i.position)||(s=this._getOrReturnCtx(e,s),V(s,{code:$.invalid_string,validation:{includes:i.value,position:i.position},message:i.message}),t.dirty()):i.kind==="toLowerCase"?e.data=e.data.toLowerCase():i.kind==="toUpperCase"?e.data=e.data.toUpperCase():i.kind==="startsWith"?e.data.startsWith(i.value)||(s=this._getOrReturnCtx(e,s),V(s,{code:$.invalid_string,validation:{startsWith:i.value},message:i.message}),t.dirty()):i.kind==="endsWith"?e.data.endsWith(i.value)||(s=this._getOrReturnCtx(e,s),V(s,{code:$.invalid_string,validation:{endsWith:i.value},message:i.message}),t.dirty()):i.kind==="datetime"?pa(i).test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{code:$.invalid_string,validation:"datetime",message:i.message}),t.dirty()):i.kind==="date"?On.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{code:$.invalid_string,validation:"date",message:i.message}),t.dirty()):i.kind==="time"?new RegExp(`^${ha(i)}$`).test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{code:$.invalid_string,validation:"time",message:i.message}),t.dirty()):i.kind==="duration"?Mn.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"duration",code:$.invalid_string,message:i.message}),t.dirty()):i.kind==="ip"?(a=e.data,((l=i.version)!=="v4"&&l||!Tn.test(a))&&(l!=="v6"&&l||!Rn.test(a))&&(s=this._getOrReturnCtx(e,s),V(s,{validation:"ip",code:$.invalid_string,message:i.message}),t.dirty())):i.kind==="jwt"?Ln(e.data,i.alg)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"jwt",code:$.invalid_string,message:i.message}),t.dirty()):i.kind==="cidr"?Zn(e.data,i.version)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"cidr",code:$.invalid_string,message:i.message}),t.dirty()):i.kind==="base64"?In.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"base64",code:$.invalid_string,message:i.message}),t.dirty()):i.kind==="base64url"?zn.test(e.data)||(s=this._getOrReturnCtx(e,s),V(s,{validation:"base64url",code:$.invalid_string,message:i.message}),t.dirty()):Ze.assertNever(i);var a,l;return{status:t.value,value:e.data}}_regex(e,t,s){return this.refinement(a=>e.test(a),{validation:t,code:$.invalid_string,...ie.errToObj(s)})}_addCheck(e){return new $t({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...ie.errToObj(e)})}url(e){return this._addCheck({kind:"url",...ie.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...ie.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...ie.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...ie.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...ie.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...ie.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...ie.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...ie.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...ie.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...ie.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...ie.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...ie.errToObj(e)})}datetime(e){var t,s;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,offset:(t=e==null?void 0:e.offset)!==null&&t!==void 0&&t,local:(s=e==null?void 0:e.local)!==null&&s!==void 0&&s,...ie.errToObj(e==null?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:(e==null?void 0:e.precision)===void 0?null:e==null?void 0:e.precision,...ie.errToObj(e==null?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...ie.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...ie.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t==null?void 0:t.position,...ie.errToObj(t==null?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...ie.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...ie.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...ie.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...ie.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...ie.errToObj(t)})}nonempty(e){return this.min(1,ie.errToObj(e))}trim(){return new $t({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new $t({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new $t({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isCIDR(){return!!this._def.checks.find(e=>e.kind==="cidr")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get isBase64url(){return!!this._def.checks.find(e=>e.kind==="base64url")}get minLength(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}function Pn(r,e){const t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,a=t>s?t:s;return parseInt(r.toFixed(a).replace(".",""))%parseInt(e.toFixed(a).replace(".",""))/Math.pow(10,a)}$t.create=r=>{var e;return new $t({checks:[],typeName:fe.ZodString,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Ae(r)})};class Vt extends Te{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==W.number){const a=this._getOrReturnCtx(e);return V(a,{code:$.invalid_type,expected:W.number,received:a.parsedType}),_e}let t;const s=new ct;for(const a of this._def.checks)a.kind==="int"?Ze.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),V(t,{code:$.invalid_type,expected:"integer",received:"float",message:a.message}),s.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),V(t,{code:$.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),V(t,{code:$.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),s.dirty()):a.kind==="multipleOf"?Pn(e.data,a.value)!==0&&(t=this._getOrReturnCtx(e,t),V(t,{code:$.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),V(t,{code:$.not_finite,message:a.message}),s.dirty()):Ze.assertNever(a);return{status:s.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,ie.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ie.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ie.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ie.toString(t))}setLimit(e,t,s,a){return new Vt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ie.toString(a)}]})}_addCheck(e){return new Vt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:ie.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:ie.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:ie.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:ie.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:ie.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ie.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:ie.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:ie.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:ie.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&Ze.isInteger(e.value))}get isFinite(){let e=null,t=null;for(const s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}}Vt.create=r=>new Vt({checks:[],typeName:fe.ZodNumber,coerce:(r==null?void 0:r.coerce)||!1,...Ae(r)});class qt extends Te{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==W.bigint)return this._getInvalidInput(e);let t;const s=new ct;for(const a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(t=this._getOrReturnCtx(e,t),V(t,{code:$.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(t=this._getOrReturnCtx(e,t),V(t,{code:$.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),s.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),V(t,{code:$.not_multiple_of,multipleOf:a.value,message:a.message}),s.dirty()):Ze.assertNever(a);return{status:s.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.bigint,received:t.parsedType}),_e}gte(e,t){return this.setLimit("min",e,!0,ie.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ie.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ie.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ie.toString(t))}setLimit(e,t,s,a){return new qt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:ie.toString(a)}]})}_addCheck(e){return new qt({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:ie.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:ie.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:ie.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:ie.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ie.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}}qt.create=r=>{var e;return new qt({checks:[],typeName:fe.ZodBigInt,coerce:(e=r==null?void 0:r.coerce)!==null&&e!==void 0&&e,...Ae(r)})};class Ms extends Te{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==W.boolean){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.boolean,received:t.parsedType}),_e}return ht(e.data)}}Ms.create=r=>new Ms({typeName:fe.ZodBoolean,coerce:(r==null?void 0:r.coerce)||!1,...Ae(r)});class ts extends Te{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==W.date){const a=this._getOrReturnCtx(e);return V(a,{code:$.invalid_type,expected:W.date,received:a.parsedType}),_e}if(isNaN(e.data.getTime()))return V(this._getOrReturnCtx(e),{code:$.invalid_date}),_e;const t=new ct;let s;for(const a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(s=this._getOrReturnCtx(e,s),V(s,{code:$.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),t.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(s=this._getOrReturnCtx(e,s),V(s,{code:$.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),t.dirty()):Ze.assertNever(a);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ts({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:ie.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:ie.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}}ts.create=r=>new ts({checks:[],coerce:(r==null?void 0:r.coerce)||!1,typeName:fe.ZodDate,...Ae(r)});class Qs extends Te{_parse(e){if(this._getType(e)!==W.symbol){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.symbol,received:t.parsedType}),_e}return ht(e.data)}}Qs.create=r=>new Qs({typeName:fe.ZodSymbol,...Ae(r)});class As extends Te{_parse(e){if(this._getType(e)!==W.undefined){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.undefined,received:t.parsedType}),_e}return ht(e.data)}}As.create=r=>new As({typeName:fe.ZodUndefined,...Ae(r)});class Ts extends Te{_parse(e){if(this._getType(e)!==W.null){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.null,received:t.parsedType}),_e}return ht(e.data)}}Ts.create=r=>new Ts({typeName:fe.ZodNull,...Ae(r)});class ms extends Te{constructor(){super(...arguments),this._any=!0}_parse(e){return ht(e.data)}}ms.create=r=>new ms({typeName:fe.ZodAny,...Ae(r)});class Xt extends Te{constructor(){super(...arguments),this._unknown=!0}_parse(e){return ht(e.data)}}Xt.create=r=>new Xt({typeName:fe.ZodUnknown,...Ae(r)});class Pt extends Te{_parse(e){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.never,received:t.parsedType}),_e}}Pt.create=r=>new Pt({typeName:fe.ZodNever,...Ae(r)});class er extends Te{_parse(e){if(this._getType(e)!==W.undefined){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.void,received:t.parsedType}),_e}return ht(e.data)}}er.create=r=>new er({typeName:fe.ZodVoid,...Ae(r)});class kt extends Te{_parse(e){const{ctx:t,status:s}=this._processInputParams(e),a=this._def;if(t.parsedType!==W.array)return V(t,{code:$.invalid_type,expected:W.array,received:t.parsedType}),_e;if(a.exactLength!==null){const i=t.data.length>a.exactLength.value,o=t.data.length<a.exactLength.value;(i||o)&&(V(t,{code:i?$.too_big:$.too_small,minimum:o?a.exactLength.value:void 0,maximum:i?a.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:a.exactLength.message}),s.dirty())}if(a.minLength!==null&&t.data.length<a.minLength.value&&(V(t,{code:$.too_small,minimum:a.minLength.value,type:"array",inclusive:!0,exact:!1,message:a.minLength.message}),s.dirty()),a.maxLength!==null&&t.data.length>a.maxLength.value&&(V(t,{code:$.too_big,maximum:a.maxLength.value,type:"array",inclusive:!0,exact:!1,message:a.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>a.type._parseAsync(new Nt(t,i,t.path,o)))).then(i=>ct.mergeArray(s,i));const l=[...t.data].map((i,o)=>a.type._parseSync(new Nt(t,i,t.path,o)));return ct.mergeArray(s,l)}get element(){return this._def.type}min(e,t){return new kt({...this._def,minLength:{value:e,message:ie.toString(t)}})}max(e,t){return new kt({...this._def,maxLength:{value:e,message:ie.toString(t)}})}length(e,t){return new kt({...this._def,exactLength:{value:e,message:ie.toString(t)}})}nonempty(e){return this.min(1,e)}}function is(r){if(r instanceof Ve){const e={};for(const t in r.shape){const s=r.shape[t];e[t]=At.create(is(s))}return new Ve({...r._def,shape:()=>e})}return r instanceof kt?new kt({...r._def,type:is(r.element)}):r instanceof At?At.create(is(r.unwrap())):r instanceof Bt?Bt.create(is(r.unwrap())):r instanceof Rt?Rt.create(r.items.map(e=>is(e))):r}kt.create=(r,e)=>new kt({type:r,minLength:null,maxLength:null,exactLength:null,typeName:fe.ZodArray,...Ae(e)});class Ve extends Te{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;const e=this._def.shape(),t=Ze.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==W.object){const c=this._getOrReturnCtx(e);return V(c,{code:$.invalid_type,expected:W.object,received:c.parsedType}),_e}const{status:t,ctx:s}=this._processInputParams(e),{shape:a,keys:l}=this._getCached(),i=[];if(!(this._def.catchall instanceof Pt&&this._def.unknownKeys==="strip"))for(const c in s.data)l.includes(c)||i.push(c);const o=[];for(const c of l){const u=a[c],w=s.data[c];o.push({key:{status:"valid",value:c},value:u._parse(new Nt(s,w,s.path,c)),alwaysSet:c in s.data})}if(this._def.catchall instanceof Pt){const c=this._def.unknownKeys;if(c==="passthrough")for(const u of i)o.push({key:{status:"valid",value:u},value:{status:"valid",value:s.data[u]}});else if(c==="strict")i.length>0&&(V(s,{code:$.unrecognized_keys,keys:i}),t.dirty());else if(c!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const c=this._def.catchall;for(const u of i){const w=s.data[u];o.push({key:{status:"valid",value:u},value:c._parse(new Nt(s,w,s.path,u)),alwaysSet:u in s.data})}}return s.common.async?Promise.resolve().then(async()=>{const c=[];for(const u of o){const w=await u.key,D=await u.value;c.push({key:w,value:D,alwaysSet:u.alwaysSet})}return c}).then(c=>ct.mergeObjectSync(t,c)):ct.mergeObjectSync(t,o)}get shape(){return this._def.shape()}strict(e){return ie.errToObj,new Ve({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var a,l,i,o;const c=(i=(l=(a=this._def).errorMap)===null||l===void 0?void 0:l.call(a,t,s).message)!==null&&i!==void 0?i:s.defaultError;return t.code==="unrecognized_keys"?{message:(o=ie.errToObj(e).message)!==null&&o!==void 0?o:c}:{message:c}}}:{}})}strip(){return new Ve({...this._def,unknownKeys:"strip"})}passthrough(){return new Ve({...this._def,unknownKeys:"passthrough"})}extend(e){return new Ve({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new Ve({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:fe.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new Ve({...this._def,catchall:e})}pick(e){const t={};return Ze.objectKeys(e).forEach(s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])}),new Ve({...this._def,shape:()=>t})}omit(e){const t={};return Ze.objectKeys(this.shape).forEach(s=>{e[s]||(t[s]=this.shape[s])}),new Ve({...this._def,shape:()=>t})}deepPartial(){return is(this)}partial(e){const t={};return Ze.objectKeys(this.shape).forEach(s=>{const a=this.shape[s];e&&!e[s]?t[s]=a:t[s]=a.optional()}),new Ve({...this._def,shape:()=>t})}required(e){const t={};return Ze.objectKeys(this.shape).forEach(s=>{if(e&&!e[s])t[s]=this.shape[s];else{let a=this.shape[s];for(;a instanceof At;)a=a._def.innerType;t[s]=a}}),new Ve({...this._def,shape:()=>t})}keyof(){return va(Ze.objectKeys(this.shape))}}Ve.create=(r,e)=>new Ve({shape:()=>r,unknownKeys:"strip",catchall:Pt.create(),typeName:fe.ZodObject,...Ae(e)}),Ve.strictCreate=(r,e)=>new Ve({shape:()=>r,unknownKeys:"strict",catchall:Pt.create(),typeName:fe.ZodObject,...Ae(e)}),Ve.lazycreate=(r,e)=>new Ve({shape:r,unknownKeys:"strip",catchall:Pt.create(),typeName:fe.ZodObject,...Ae(e)});class Ns extends Te{_parse(e){const{ctx:t}=this._processInputParams(e),s=this._def.options;if(t.common.async)return Promise.all(s.map(async a=>{const l={...t,common:{...t.common,issues:[]},parent:null};return{result:await a._parseAsync({data:t.data,path:t.path,parent:l}),ctx:l}})).then(function(a){for(const i of a)if(i.result.status==="valid")return i.result;for(const i of a)if(i.result.status==="dirty")return t.common.issues.push(...i.ctx.common.issues),i.result;const l=a.map(i=>new gt(i.ctx.common.issues));return V(t,{code:$.invalid_union,unionErrors:l}),_e});{let a;const l=[];for(const o of s){const c={...t,common:{...t.common,issues:[]},parent:null},u=o._parseSync({data:t.data,path:t.path,parent:c});if(u.status==="valid")return u;u.status!=="dirty"||a||(a={result:u,ctx:c}),c.common.issues.length&&l.push(c.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;const i=l.map(o=>new gt(o));return V(t,{code:$.invalid_union,unionErrors:i}),_e}}get options(){return this._def.options}}Ns.create=(r,e)=>new Ns({options:r,typeName:fe.ZodUnion,...Ae(e)});const jt=r=>r instanceof Es?jt(r.schema):r instanceof St?jt(r.innerType()):r instanceof Is?[r.value]:r instanceof Ht?r.options:r instanceof zs?Ze.objectValues(r.enum):r instanceof Os?jt(r._def.innerType):r instanceof As?[void 0]:r instanceof Ts?[null]:r instanceof At?[void 0,...jt(r.unwrap())]:r instanceof Bt?[null,...jt(r.unwrap())]:r instanceof Tr||r instanceof Zs?jt(r.unwrap()):r instanceof Ls?jt(r._def.innerType):[];class nr extends Te{_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==W.object)return V(t,{code:$.invalid_type,expected:W.object,received:t.parsedType}),_e;const s=this.discriminator,a=t.data[s],l=this.optionsMap.get(a);return l?t.common.async?l._parseAsync({data:t.data,path:t.path,parent:t}):l._parseSync({data:t.data,path:t.path,parent:t}):(V(t,{code:$.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),_e)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){const a=new Map;for(const l of t){const i=jt(l.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(const o of i){if(a.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);a.set(o,l)}}return new nr({typeName:fe.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:a,...Ae(s)})}}function Sr(r,e){const t=Ot(r),s=Ot(e);if(r===e)return{valid:!0,data:r};if(t===W.object&&s===W.object){const a=Ze.objectKeys(e),l=Ze.objectKeys(r).filter(o=>a.indexOf(o)!==-1),i={...r,...e};for(const o of l){const c=Sr(r[o],e[o]);if(!c.valid)return{valid:!1};i[o]=c.data}return{valid:!0,data:i}}if(t===W.array&&s===W.array){if(r.length!==e.length)return{valid:!1};const a=[];for(let l=0;l<r.length;l++){const i=Sr(r[l],e[l]);if(!i.valid)return{valid:!1};a.push(i.data)}return{valid:!0,data:a}}return t===W.date&&s===W.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}class Rs extends Te{_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=(l,i)=>{if(br(l)||br(i))return _e;const o=Sr(l.value,i.value);return o.valid?(($r(l)||$r(i))&&t.dirty(),{status:t.value,value:o.data}):(V(s,{code:$.invalid_intersection_types}),_e)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([l,i])=>a(l,i)):a(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}}Rs.create=(r,e,t)=>new Rs({left:r,right:e,typeName:fe.ZodIntersection,...Ae(t)});class Rt extends Te{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==W.array)return V(s,{code:$.invalid_type,expected:W.array,received:s.parsedType}),_e;if(s.data.length<this._def.items.length)return V(s,{code:$.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),_e;!this._def.rest&&s.data.length>this._def.items.length&&(V(s,{code:$.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const a=[...s.data].map((l,i)=>{const o=this._def.items[i]||this._def.rest;return o?o._parse(new Nt(s,l,s.path,i)):null}).filter(l=>!!l);return s.common.async?Promise.all(a).then(l=>ct.mergeArray(t,l)):ct.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new Rt({...this._def,rest:e})}}Rt.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Rt({items:r,typeName:fe.ZodTuple,rest:null,...Ae(e)})};class ir extends Te{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==W.object)return V(s,{code:$.invalid_type,expected:W.object,received:s.parsedType}),_e;const a=[],l=this._def.keyType,i=this._def.valueType;for(const o in s.data)a.push({key:l._parse(new Nt(s,o,s.path,o)),value:i._parse(new Nt(s,s.data[o],s.path,o)),alwaysSet:o in s.data});return s.common.async?ct.mergeObjectAsync(t,a):ct.mergeObjectSync(t,a)}get element(){return this._def.valueType}static create(e,t,s){return new ir(t instanceof Te?{keyType:e,valueType:t,typeName:fe.ZodRecord,...Ae(s)}:{keyType:$t.create(),valueType:e,typeName:fe.ZodRecord,...Ae(t)})}}class tr extends Te{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==W.map)return V(s,{code:$.invalid_type,expected:W.map,received:s.parsedType}),_e;const a=this._def.keyType,l=this._def.valueType,i=[...s.data.entries()].map(([o,c],u)=>({key:a._parse(new Nt(s,o,s.path,[u,"key"])),value:l._parse(new Nt(s,c,s.path,[u,"value"]))}));if(s.common.async){const o=new Map;return Promise.resolve().then(async()=>{for(const c of i){const u=await c.key,w=await c.value;if(u.status==="aborted"||w.status==="aborted")return _e;u.status!=="dirty"&&w.status!=="dirty"||t.dirty(),o.set(u.value,w.value)}return{status:t.value,value:o}})}{const o=new Map;for(const c of i){const u=c.key,w=c.value;if(u.status==="aborted"||w.status==="aborted")return _e;u.status!=="dirty"&&w.status!=="dirty"||t.dirty(),o.set(u.value,w.value)}return{status:t.value,value:o}}}}tr.create=(r,e,t)=>new tr({valueType:e,keyType:r,typeName:fe.ZodMap,...Ae(t)});class ss extends Te{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==W.set)return V(s,{code:$.invalid_type,expected:W.set,received:s.parsedType}),_e;const a=this._def;a.minSize!==null&&s.data.size<a.minSize.value&&(V(s,{code:$.too_small,minimum:a.minSize.value,type:"set",inclusive:!0,exact:!1,message:a.minSize.message}),t.dirty()),a.maxSize!==null&&s.data.size>a.maxSize.value&&(V(s,{code:$.too_big,maximum:a.maxSize.value,type:"set",inclusive:!0,exact:!1,message:a.maxSize.message}),t.dirty());const l=this._def.valueType;function i(c){const u=new Set;for(const w of c){if(w.status==="aborted")return _e;w.status==="dirty"&&t.dirty(),u.add(w.value)}return{status:t.value,value:u}}const o=[...s.data.values()].map((c,u)=>l._parse(new Nt(s,c,s.path,u)));return s.common.async?Promise.all(o).then(c=>i(c)):i(o)}min(e,t){return new ss({...this._def,minSize:{value:e,message:ie.toString(t)}})}max(e,t){return new ss({...this._def,maxSize:{value:e,message:ie.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}ss.create=(r,e)=>new ss({valueType:r,minSize:null,maxSize:null,typeName:fe.ZodSet,...Ae(e)});class cs extends Te{constructor(){super(...arguments),this.validate=this.implement}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==W.function)return V(t,{code:$.invalid_type,expected:W.function,received:t.parsedType}),_e;function s(o,c){return Ks({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ws(),vs].filter(u=>!!u),issueData:{code:$.invalid_arguments,argumentsError:c}})}function a(o,c){return Ks({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,Ws(),vs].filter(u=>!!u),issueData:{code:$.invalid_return_type,returnTypeError:c}})}const l={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof gs){const o=this;return ht(async function(...c){const u=new gt([]),w=await o._def.args.parseAsync(c,l).catch(A=>{throw u.addIssue(s(c,A)),u}),D=await Reflect.apply(i,this,w);return await o._def.returns._def.type.parseAsync(D,l).catch(A=>{throw u.addIssue(a(D,A)),u})})}{const o=this;return ht(function(...c){const u=o._def.args.safeParse(c,l);if(!u.success)throw new gt([s(c,u.error)]);const w=Reflect.apply(i,this,u.data),D=o._def.returns.safeParse(w,l);if(!D.success)throw new gt([a(w,D.error)]);return D.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new cs({...this._def,args:Rt.create(e).rest(Xt.create())})}returns(e){return new cs({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new cs({args:e||Rt.create([]).rest(Xt.create()),returns:t||Xt.create(),typeName:fe.ZodFunction,...Ae(s)})}}class Es extends Te{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}Es.create=(r,e)=>new Es({getter:r,typeName:fe.ZodLazy,...Ae(e)});class Is extends Te{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return V(t,{received:t.data,code:$.invalid_literal,expected:this._def.value}),_e}return{status:"valid",value:e.data}}get value(){return this._def.value}}function va(r,e){return new Ht({values:r,typeName:fe.ZodEnum,...Ae(e)})}Is.create=(r,e)=>new Is({value:r,typeName:fe.ZodLiteral,...Ae(e)});class Ht extends Te{constructor(){super(...arguments),bs.set(this,void 0)}_parse(e){if(typeof e.data!="string"){const t=this._getOrReturnCtx(e),s=this._def.values;return V(t,{expected:Ze.joinValues(s),received:t.parsedType,code:$.invalid_type}),_e}if(Xs(this,bs)||ca(this,bs,new Set(this._def.values)),!Xs(this,bs).has(e.data)){const t=this._getOrReturnCtx(e),s=this._def.values;return V(t,{received:t.data,code:$.invalid_enum_value,options:s}),_e}return ht(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return Ht.create(e,{...this._def,...t})}exclude(e,t=this._def){return Ht.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}}bs=new WeakMap,Ht.create=va;class zs extends Te{constructor(){super(...arguments),$s.set(this,void 0)}_parse(e){const t=Ze.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==W.string&&s.parsedType!==W.number){const a=Ze.objectValues(t);return V(s,{expected:Ze.joinValues(a),received:s.parsedType,code:$.invalid_type}),_e}if(Xs(this,$s)||ca(this,$s,new Set(Ze.getValidEnumValues(this._def.values))),!Xs(this,$s).has(e.data)){const a=Ze.objectValues(t);return V(s,{received:s.data,code:$.invalid_enum_value,options:a}),_e}return ht(e.data)}get enum(){return this._def.values}}$s=new WeakMap,zs.create=(r,e)=>new zs({values:r,typeName:fe.ZodNativeEnum,...Ae(e)});class gs extends Te{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==W.promise&&t.common.async===!1)return V(t,{code:$.invalid_type,expected:W.promise,received:t.parsedType}),_e;const s=t.parsedType===W.promise?t.data:Promise.resolve(t.data);return ht(s.then(a=>this._def.type.parseAsync(a,{path:t.path,errorMap:t.common.contextualErrorMap})))}}gs.create=(r,e)=>new gs({type:r,typeName:fe.ZodPromise,...Ae(e)});class St extends Te{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===fe.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:s}=this._processInputParams(e),a=this._def.effect||null,l={addIssue:i=>{V(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(l.addIssue=l.addIssue.bind(l),a.type==="preprocess"){const i=a.transform(s.data,l);if(s.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return _e;const c=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return c.status==="aborted"?_e:c.status==="dirty"||t.value==="dirty"?Ys(c.value):c});{if(t.value==="aborted")return _e;const o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?_e:o.status==="dirty"||t.value==="dirty"?Ys(o.value):o}}if(a.type==="refinement"){const i=o=>{const c=a.refinement(o,l);if(s.common.async)return Promise.resolve(c);if(c instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){const o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?_e:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?_e:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(a.type==="transform"){if(s.common.async===!1){const i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!es(i))return i;const o=a.transform(i.value,l);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>es(i)?Promise.resolve(a.transform(i.value,l)).then(o=>({status:t.value,value:o})):i)}Ze.assertNever(a)}}St.create=(r,e,t)=>new St({schema:r,typeName:fe.ZodEffects,effect:e,...Ae(t)}),St.createWithPreprocess=(r,e,t)=>new St({schema:e,effect:{type:"preprocess",transform:r},typeName:fe.ZodEffects,...Ae(t)});class At extends Te{_parse(e){return this._getType(e)===W.undefined?ht(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}At.create=(r,e)=>new At({innerType:r,typeName:fe.ZodOptional,...Ae(e)});class Bt extends Te{_parse(e){return this._getType(e)===W.null?ht(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Bt.create=(r,e)=>new Bt({innerType:r,typeName:fe.ZodNullable,...Ae(e)});class Os extends Te{_parse(e){const{ctx:t}=this._processInputParams(e);let s=t.data;return t.parsedType===W.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Os.create=(r,e)=>new Os({innerType:r,typeName:fe.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...Ae(e)});class Ls extends Te{_parse(e){const{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},a=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return xs(a)?a.then(l=>({status:"valid",value:l.status==="valid"?l.value:this._def.catchValue({get error(){return new gt(s.common.issues)},input:s.data})})):{status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new gt(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}}Ls.create=(r,e)=>new Ls({innerType:r,typeName:fe.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...Ae(e)});class sr extends Te{_parse(e){if(this._getType(e)!==W.nan){const t=this._getOrReturnCtx(e);return V(t,{code:$.invalid_type,expected:W.nan,received:t.parsedType}),_e}return{status:"valid",value:e.data}}}sr.create=r=>new sr({typeName:fe.ZodNaN,...Ae(r)});const Fn=Symbol("zod_brand");class Tr extends Te{_parse(e){const{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}}class js extends Te{_parse(e){const{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{const a=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?_e:a.status==="dirty"?(t.dirty(),Ys(a.value)):this._def.out._parseAsync({data:a.value,path:s.path,parent:s})})();{const a=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?_e:a.status==="dirty"?(t.dirty(),{status:"dirty",value:a.value}):this._def.out._parseSync({data:a.value,path:s.path,parent:s})}}static create(e,t){return new js({in:e,out:t,typeName:fe.ZodPipeline})}}class Zs extends Te{_parse(e){const t=this._def.innerType._parse(e),s=a=>(es(a)&&(a.value=Object.freeze(a.value)),a);return xs(t)?t.then(a=>s(a)):s(t)}unwrap(){return this._def.innerType}}function Dr(r,e={},t){return r?ms.create().superRefine((s,a)=>{var l,i;if(!r(s)){const o=typeof e=="function"?e(s):typeof e=="string"?{message:e}:e,c=(i=(l=o.fatal)!==null&&l!==void 0?l:t)===null||i===void 0||i,u=typeof o=="string"?{message:o}:o;a.addIssue({code:"custom",...u,fatal:c})}}):ms.create()}Zs.create=(r,e)=>new Zs({innerType:r,typeName:fe.ZodReadonly,...Ae(e)});const jn={object:Ve.lazycreate};var fe;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(fe||(fe={}));const Ur=$t.create,Vr=Vt.create,Dn=sr.create,Un=qt.create,qr=Ms.create,Vn=ts.create,qn=Qs.create,Hn=As.create,Bn=Ts.create,Jn=ms.create,Gn=Xt.create,Wn=Pt.create,Kn=er.create,Yn=kt.create,Xn=Ve.create,Qn=Ve.strictCreate,ei=Ns.create,ti=nr.create,si=Rs.create,ri=Rt.create,ai=ir.create,ni=tr.create,ii=ss.create,oi=cs.create,li=Es.create,di=Is.create,ci=Ht.create,ui=zs.create,hi=gs.create,Hr=St.create,pi=At.create,vi=Bt.create,mi=St.createWithPreprocess,gi=js.create,fi={string:r=>$t.create({...r,coerce:!0}),number:r=>Vt.create({...r,coerce:!0}),boolean:r=>Ms.create({...r,coerce:!0}),bigint:r=>qt.create({...r,coerce:!0}),date:r=>ts.create({...r,coerce:!0})},yi=_e;var Pe=Object.freeze({__proto__:null,defaultErrorMap:vs,setErrorMap:function(r){da=r},getErrorMap:Ws,makeIssue:Ks,EMPTY_PATH:[],addIssueToContext:V,ParseStatus:ct,INVALID:_e,DIRTY:Ys,OK:ht,isAborted:br,isDirty:$r,isValid:es,isAsync:xs,get util(){return Ze},get objectUtil(){return wr},ZodParsedType:W,getParsedType:Ot,ZodType:Te,datetimeRegex:pa,ZodString:$t,ZodNumber:Vt,ZodBigInt:qt,ZodBoolean:Ms,ZodDate:ts,ZodSymbol:Qs,ZodUndefined:As,ZodNull:Ts,ZodAny:ms,ZodUnknown:Xt,ZodNever:Pt,ZodVoid:er,ZodArray:kt,ZodObject:Ve,ZodUnion:Ns,ZodDiscriminatedUnion:nr,ZodIntersection:Rs,ZodTuple:Rt,ZodRecord:ir,ZodMap:tr,ZodSet:ss,ZodFunction:cs,ZodLazy:Es,ZodLiteral:Is,ZodEnum:Ht,ZodNativeEnum:zs,ZodPromise:gs,ZodEffects:St,ZodTransformer:St,ZodOptional:At,ZodNullable:Bt,ZodDefault:Os,ZodCatch:Ls,ZodNaN:sr,BRAND:Fn,ZodBranded:Tr,ZodPipeline:js,ZodReadonly:Zs,custom:Dr,Schema:Te,ZodSchema:Te,late:jn,get ZodFirstPartyTypeKind(){return fe},coerce:fi,any:Jn,array:Yn,bigint:Un,boolean:qr,date:Vn,discriminatedUnion:ti,effect:Hr,enum:ci,function:oi,instanceof:(r,e={message:`Input not instance of ${r.name}`})=>Dr(t=>t instanceof r,e),intersection:si,lazy:li,literal:di,map:ni,nan:Dn,nativeEnum:ui,never:Wn,null:Bn,nullable:vi,number:Vr,object:Xn,oboolean:()=>qr().optional(),onumber:()=>Vr().optional(),optional:pi,ostring:()=>Ur().optional(),pipeline:gi,preprocess:mi,promise:hi,record:ai,set:ii,strictObject:Qn,string:Ur,symbol:qn,transformer:Hr,tuple:ri,undefined:Hn,union:ei,unknown:Gn,void:Kn,NEVER:yi,ZodIssueCode:$,quotelessJson:r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:gt});class dt extends Error{constructor(e){super(e),this.name="MCPServerError",Object.setPrototypeOf(this,dt.prototype)}}const Zt=Pe.object({name:Pe.string().optional(),title:Pe.string().optional(),type:Pe.enum(["stdio","http","sse"]).optional(),command:Pe.string().optional(),args:Pe.array(Pe.union([Pe.string(),Pe.number(),Pe.boolean()])).optional(),env:Pe.record(Pe.union([Pe.string(),Pe.number(),Pe.boolean(),Pe.null(),Pe.undefined()])).optional(),url:Pe.string().optional()}).passthrough();function bt(r){return(r==null?void 0:r.type)==="http"||(r==null?void 0:r.type)==="sse"}function os(r){return(r==null?void 0:r.type)==="stdio"}function Hs(r){return bt(r)?r.url:os(r)?r.command:""}const _i=Pe.array(Zt),Ci=Pe.object({servers:Pe.array(Zt)}),wi=Pe.object({mcpServers:Pe.array(Zt)}),bi=Pe.object({servers:Pe.record(Pe.unknown())}),$i=Pe.object({mcpServers:Pe.record(Pe.unknown())}),Si=Pe.record(Pe.unknown()),ki=Zt.refine(r=>{const e=r.command!==void 0,t=r.url!==void 0;if(!e&&!t)return!1;const s=new Set(["name","title","type","command","args","env","url"]);return Object.keys(r).every(a=>s.has(a))},{message:"Single server object must have valid server properties"});function Wt(r){try{const e=Zt.transform(t=>{let s;if(t.type)s=t.type;else if(t.url)s="http";else{if(!t.command)throw new Error("Server must have either 'command' (for stdio) or 'url' (for http/sse) property");s="stdio"}if(s==="http"||s==="sse"){if(!t.url)throw new Error(`${s.toUpperCase()} server must have a 'url' property`);return{type:s,name:t.name||t.title||t.url,url:t.url}}{const a=t.command||"",l=t.args?t.args.map(u=>String(u)):[];if(!a)throw new Error("Stdio server must have a 'command' property");const i=l.length>0?`${a} ${l.join(" ")}`:a,o=t.name||t.title||(a?a.split(" ")[0]:""),c=t.env?Object.fromEntries(Object.entries(t.env).filter(([u,w])=>w!=null).map(([u,w])=>[u,String(w)])):void 0;return{type:"stdio",name:o,command:i,arguments:"",useShellInterpolation:!0,env:Object.keys(c||{}).length>0?c:void 0}}}).refine(t=>!!t.name,{message:"Server must have a name",path:["name"]}).refine(t=>t.type==="http"||t.type==="sse"?!!t.url:!!t.command,{message:"Server must have either 'command' (for stdio) or 'url' (for http/sse)",path:["command","url"]}).safeParse(r);if(!e.success)throw new dt(e.error.message);return e.data}catch(e){throw e instanceof Error?new dt(`Invalid server configuration: ${e.message}`):new dt("Invalid server configuration")}}class Ds{constructor(e){ze(this,"servers",nt([]));this.host=e,this.loadServersFromStorage()}handleMessageFromExtension(e){const t=e.data;if(t.type===De.getStoredMCPServersResponse){const s=t.data;return Array.isArray(s)&&this.servers.set(s),!0}return!1}async importServersFromJSON(e){return this.importFromJSON(e)}loadServersFromStorage(){try{this.host.postMessage({type:De.getStoredMCPServers})}catch(e){console.error("Failed to load MCP servers:",e),this.servers.set([])}}saveServers(e){try{this.host.postMessage({type:De.setStoredMCPServers,data:e})}catch(t){throw console.error("Failed to save MCP servers:",t),new dt("Failed to save MCP servers")}}getServers(){return this.servers}addServer(e){this.checkExistingServerName(e.name),this.servers.update(t=>{const s=[...t,{...e,id:crypto.randomUUID()}];return this.saveServers(s),s})}addServers(e){for(const t of e)this.checkExistingServerName(t.name);this.servers.update(t=>{const s=[...t,...e.map(a=>({...a,id:crypto.randomUUID()}))];return this.saveServers(s),s})}checkExistingServerName(e,t){const s=vr(this.servers).find(a=>a.name===e);if(s&&(s==null?void 0:s.id)!==t)throw new dt(`Server name '${e}' already exists`)}updateServer(e){this.checkExistingServerName(e.name,e.id),this.servers.update(t=>{const s=t.map(a=>a.id===e.id?e:a);return this.saveServers(s),s})}deleteServer(e){this.servers.update(t=>{const s=t.filter(a=>a.id!==e);return this.saveServers(s),s})}toggleDisabledServer(e){this.servers.update(t=>{const s=t.map(a=>a.id===e?{...a,disabled:!a.disabled}:a);return this.saveServers(s),s})}static convertServerToJSON(e){if(bt(e))return JSON.stringify({mcpServers:{[e.name]:{url:e.url,type:e.type}}},null,2);{const t=e;return JSON.stringify({mcpServers:{[t.name]:{command:t.command.split(" ")[0],args:t.command.split(" ").slice(1),env:t.env}}},null,2)}}static parseServerValidationMessages(e){const t=new Map,s=new Map;e.forEach(l=>{var o,c;const i=(o=l.tools)==null?void 0:o.filter(u=>!u.enabled).map(u=>u.definition.mcp_tool_name);l.disabled?t.set(l.id,"MCP server has been manually disabled"):l.tools&&l.tools.length===0?t.set(l.id,"No tools are available for this MCP server"):i&&i.length===((c=l.tools)==null?void 0:c.length)?t.set(l.id,"All tools for this MCP server have validation errors: "+i.join(", ")):i&&i.length>0&&s.set(l.id,"MCP server has validation errors in the following tools which have been disabled: "+i.join(", "))});const a=this.parseDuplicateServerIds(e);return{errors:new Map([...t,...a]),warnings:s}}static parseDuplicateServerIds(e){const t=new Map;for(const a of e)t.has(a.name)||t.set(a.name,[]),t.get(a.name).push(a.id);const s=new Map;for(const[,a]of t)if(a.length>1)for(let l=1;l<a.length;l++)s.set(a[l],"MCP server is disabled due to duplicate server names");return s}static convertParsedServerToWebview(e){const{tools:t,...s}=e;return{...s,tools:void 0}}static parseServerConfigFromJSON(e){return function(s){try{const a=JSON.parse(s),l=Pe.union([_i.transform(i=>i.map(o=>Wt(o))),Ci.transform(i=>i.servers.map(o=>Wt(o))),wi.transform(i=>i.mcpServers.map(o=>Wt(o))),bi.transform(i=>Object.entries(i.servers).map(([o,c])=>{const u=Zt.parse(c);return Wt({...u,name:u.name||o})})),$i.transform(i=>Object.entries(i.mcpServers).map(([o,c])=>{const u=Zt.parse(c);return Wt({...u,name:u.name||o})})),ki.transform(i=>[Wt(i)]),Si.transform(i=>{if(!Object.values(i).some(o=>{const c=Zt.safeParse(o);return c.success&&(c.data.command!==void 0||c.data.url!==void 0)}))throw new Error("No command or url property found in any server config");return Object.entries(i).map(([o,c])=>{const u=Zt.parse(c);return Wt({...u,name:u.name||o})})})]).safeParse(a);if(l.success)return l.data;throw new dt("Invalid JSON format. Expected an array of servers or an object with a 'servers' property.")}catch(a){throw a instanceof dt?a:new dt("Failed to parse MCP servers from JSON. Please check the format.")}}(e).map(s=>this.convertParsedServerToWebview(s))}importFromJSON(e){try{const t=Ds.parseServerConfigFromJSON(e),s=vr(this.servers),a=new Set(s.map(l=>l.name));for(const l of t){if(!l.name)throw new dt("All servers must have a name.");if(a.has(l.name))throw new dt(`A server with the name '${l.name}' already exists.`);a.add(l.name)}return this.servers.update(l=>{const i=[...l,...t.map(o=>({...o,id:crypto.randomUUID()}))];return this.saveServers(i),i}),t.length}catch(t){throw t instanceof dt?t:new dt("Failed to import MCP servers from JSON. Please check the format.")}}}class xi{constructor(e){ze(this,"_terminalSettings",nt({supportedShells:[],selectedShell:void 0,startupScript:void 0}));this._host=e,this.requestTerminalSettings()}handleMessageFromExtension(e){const t=e.data;return t.type===De.terminalSettingsResponse&&(this._terminalSettings.set(t.data),!0)}getTerminalSettings(){return this._terminalSettings}requestTerminalSettings(){this._host.postMessage({type:De.getTerminalSettings})}updateSelectedShell(e){this._terminalSettings.update(t=>({...t,selectedShell:e})),this._host.postMessage({type:De.updateTerminalSettings,data:{selectedShell:e}})}updateStartupScript(e){this._terminalSettings.update(t=>({...t,startupScript:e})),this._host.postMessage({type:De.updateTerminalSettings,data:{startupScript:e}})}}const Ss=class Ss{constructor(e){ze(this,"_swarmModeSettings",nt(qs));ze(this,"_isLoaded",!1);ze(this,"_pollInterval",null);ze(this,"_lastKnownSettingsHash","");ze(this,"dispose",()=>{this.stopPolling()});this._msgBroker=e,this.initialize(),this.startPolling()}get getCurrentSettings(){return this._swarmModeSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:ur.getSwarmModeSettings});e.data&&(this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=JSON.stringify(e.data)),this._isLoaded=!0}catch(e){console.warn("Failed to load swarm mode settings, using defaults:",e),this._swarmModeSettings.set(qs),this._lastKnownSettingsHash=JSON.stringify(qs),this._isLoaded=!0}}async updateSettings(e){try{const t=await this._msgBroker.sendToSidecar({type:ur.updateSwarmModeSettings,data:e});t.data&&(this._swarmModeSettings.set(t.data),this._lastKnownSettingsHash=JSON.stringify(t.data))}catch(t){throw console.error("Failed to update swarm mode settings:",t),t}}async setEnabled(e){await this.updateSettings({enabled:e})}async resetToDefaults(){await this.updateSettings(qs)}updateEnabled(e){this.setEnabled(e).catch(t=>{console.error("Failed to update enabled setting:",t)})}startPolling(){this._pollInterval=setInterval(()=>{this.checkForUpdates()},Ss.POLLING_INTERVAL_MS)}stopPolling(){this._pollInterval!==null&&(clearInterval(this._pollInterval),this._pollInterval=null)}async checkForUpdates(){try{const e=await this._msgBroker.sendToSidecar({type:ur.getSwarmModeSettings}),t=JSON.stringify(e.data);this._lastKnownSettingsHash&&t!==this._lastKnownSettingsHash&&e.data&&this._swarmModeSettings.set(e.data),this._lastKnownSettingsHash=t}catch(e){console.warn("Failed to check for swarm mode settings updates:",e)}}};ze(Ss,"key","swarmModeModel"),ze(Ss,"POLLING_INTERVAL_MS",5e3);let Ps=Ss;var xt=(r=>(r.file="file",r.folder="folder",r))(xt||{});class Ut{constructor(e,t){ze(this,"subscribe");ze(this,"set");ze(this,"update");ze(this,"handleMessageFromExtension",async e=>{const t=e.data;switch(t.type){case De.wsContextSourceFoldersChanged:case De.wsContextFolderContentsChanged:this.updateSourceFolders(await this.getSourceFolders());break;case De.sourceFoldersSyncStatus:this.update(s=>({...s,syncStatus:t.data.status}))}});ze(this,"getSourceFolders",async()=>(await this.asyncMsgSender.send({type:De.wsContextGetSourceFoldersRequest},1e4)).data.workspaceFolders);ze(this,"getChildren",async e=>(await this.asyncMsgSender.send({type:De.wsContextGetChildrenRequest,data:{fileId:e}},1e4)).data.children.map(t=>t.type==="folder"?{...t,children:[],expanded:!1}:{...t}).sort((t,s)=>t.type===s.type?t.name.localeCompare(s.name):t.type==="folder"?-1:1));this.host=e,this.asyncMsgSender=t;const{subscribe:s,set:a,update:l}=nt({sourceFolders:[],sourceTree:[],syncStatus:gr.done});this.subscribe=s,this.set=a,this.update=l,this.getSourceFolders().then(i=>{this.update(o=>({...o,sourceFolders:i,sourceTree:Ut.sourceFoldersToSourceNodes(i)}))})}async expandNode(e){e.children=await this.getChildren(e.fileId),e.expanded=!0,this.update(t=>t)}collapseNode(e){this.update(t=>(e.children=[],e.expanded=!1,t))}toggleNode(e){e.type==="folder"&&e.inclusionState!==ft.excluded&&(e.expanded?this.collapseNode(e):this.expandNode(e))}addMoreSourceFolders(){this.host.postMessage({type:De.wsContextAddMoreSourceFolders})}removeSourceFolder(e){this.host.postMessage({type:De.wsContextRemoveSourceFolder,data:e})}requestRefresh(){this.host.postMessage({type:De.wsContextUserRequestedRefresh})}async updateSourceFolders(e){let t=vr(this);const s=await this.getRefreshedSourceTree(t.sourceTree,e);this.update(a=>({...a,sourceFolders:e,sourceTree:s}))}async getRefreshedSourceTree(e,t){const s=Ut.sourceFoldersToSourceNodes(t);return this.getRefreshedSourceTreeRecurse(e,s)}async getRefreshedSourceTreeRecurse(e,t){const s=new Map(e.map(a=>[JSON.stringify([a.fileId.folderRoot,a.fileId.relPath]),a]));for(let a of t){const l=Ut.fileIdToString(a.fileId);if(a.type==="folder"){const i=s.get(l);i&&(a.expanded=i.type==="folder"&&i.expanded,a.expanded&&(a.children=await this.getChildren(a.fileId),a.children=await this.getRefreshedSourceTreeRecurse(i.children,a.children)))}}return t}static fileIdToString(e){return JSON.stringify([e.folderRoot,e.relPath])}static sourceFoldersToSourceNodes(e){return e.filter(t=>!t.isNestedFolder&&!t.isPending).sort((t,s)=>t.name.localeCompare(s.name)).map(t=>({name:t.name,fileId:t.fileId,children:[],expanded:!1,type:"folder",inclusionState:t.inclusionState,reason:"",trackedFileCount:t.trackedFileCount}))}}var Mi=v('<div><!> <!> <span class="name svelte-1skknri"> <span class="folderRoot svelte-1skknri"> </span></span> <!></div>'),Ai=v('<div class="source-folder svelte-1skknri"><!> <div role="button" tabindex="0" class="add-more svelte-1skknri"><!> Add more...</div></div>');const Ti="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM5.86301%208.67273L9.44256%203.9L8.24256%203L5.12729%207.15368L3.17471%205.59162L2.23767%206.76292L4.79449%208.80838L5.86301%208.67273Z'%20fill='%23388A34'/%3e%3c/svg%3e",Ni="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01119C3.65328%200.351896%204.81332%200%206%200C7.5907%200.00195419%209.11569%200.634726%2010.2405%201.75953C11.3653%202.88433%2011.998%204.40933%2012%206.00003C12%207.18673%2011.6481%208.34677%2010.9888%209.33347C10.3295%2010.3202%209.39246%2011.0892%208.2961%2011.5433C7.19975%2011.9975%205.99335%2012.1163%204.82946%2011.8848C3.66558%2011.6533%202.59648%2011.0818%201.75736%2010.2427C0.918247%209.40358%200.346802%208.33447%200.115291%207.17058C-0.11622%206.00669%200.00259969%204.80028%200.456726%203.70392C0.910851%202.60756%201.67989%201.67048%202.66658%201.01119ZM6.00007%207.07359L8.1213%209.19482L9.18196%208.13416L7.06073%206.01292L9.18198%203.89166L8.12132%202.83099L6.00007%204.95225L3.87866%202.83083L2.818%203.89149L4.93941%206.01292L2.81802%208.13432L3.87868%209.19499L6.00007%207.07359Z'%20fill='%23E51400'/%3e%3c/svg%3e",Ri="data:image/svg+xml,%3csvg%20width='12'%20height='12'%20viewBox='0%200%2012%2012'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cpath%20fill-rule='evenodd'%20clip-rule='evenodd'%20d='M2.66658%201.01118C3.65328%200.351894%204.81331%200%206%200C7.5907%200.00195418%209.11569%200.634723%2010.2405%201.75952C11.3653%202.88431%2011.998%204.4093%2012%206C12%207.18669%2011.6481%208.34672%2010.9888%209.33342C10.3295%2010.3201%209.39246%2011.0891%208.2961%2011.5433C7.19975%2011.9974%205.99335%2012.1162%204.82946%2011.8847C3.66557%2011.6532%202.59648%2011.0818%201.75736%2010.2426C0.918247%209.40352%200.346802%208.33443%200.115291%207.17054C-0.11622%206.00666%200.00259969%204.80025%200.456725%203.7039C0.910851%202.60754%201.67989%201.67047%202.66658%201.01118ZM3.66667%205.83333C3.66667%205.99815%203.61779%206.15927%203.52623%206.29631C3.43466%206.43335%203.30451%206.54016%203.15224%206.60323C2.99997%206.66631%202.83241%206.68281%202.67076%206.65065C2.50911%206.6185%202.36062%206.53913%202.24408%206.42259C2.12753%206.30605%202.04817%206.15756%202.01601%205.99591C1.98386%205.83426%202.00036%205.6667%202.06343%205.51443C2.12651%205.36216%202.23332%205.23201%202.37036%205.14044C2.5074%205.04887%202.66852%205%202.83333%205C3.05435%205%203.26631%205.0878%203.42259%205.24408C3.57887%205.40036%203.66667%205.61232%203.66667%205.83333ZM6.83333%205.83333C6.83333%205.99815%206.78446%206.15927%206.69289%206.29631C6.60132%206.43335%206.47117%206.54016%206.3189%206.60323C6.16663%206.66631%205.99908%206.68281%205.83742%206.65065C5.67577%206.6185%205.52729%206.53913%205.41074%206.42259C5.2942%206.30605%205.21483%206.15756%205.18268%205.99591C5.15052%205.83426%205.16703%205.6667%205.2301%205.51443C5.29317%205.36216%205.39998%205.23201%205.53702%205.14044C5.67407%205.04887%205.83518%205%206%205C6.22101%205%206.43297%205.0878%206.58926%205.24408C6.74554%205.40036%206.83333%205.61232%206.83333%205.83333ZM9.85956%206.29631C9.95113%206.15927%2010%205.99815%2010%205.83333C10%205.61232%209.9122%205.40036%209.75592%205.24408C9.59964%205.0878%209.38768%205%209.16667%205C9.00185%205%208.84073%205.04887%208.70369%205.14044C8.56665%205.23201%208.45984%205.36216%208.39677%205.51443C8.33369%205.6667%208.31719%205.83426%208.34935%205.99591C8.3815%206.15756%208.46087%206.30605%208.57741%206.42259C8.69395%206.53913%208.84244%206.6185%209.00409%206.65065C9.16574%206.68281%209.3333%206.66631%209.48557%206.60323C9.63784%206.54016%209.76799%206.43335%209.85956%206.29631Z'%20fill='%23388A34'/%3e%3c/svg%3e";var Ei=v('<div class="children-container"></div>'),Ii=v('<div><div role="treeitem" aria-selected="false" tabindex="0"><!> <span class="name svelte-sympus"> </span> <!> <img/></div> <!></div>');function ma(r,e){Be(e,!1);let t=C(e,"data",8),s=C(e,"wsContextModel",8),a=C(e,"indentLevel",8);const l=()=>{s().toggleNode(t())},i={[ft.included]:Ti,[ft.excluded]:Ni,[ft.partial]:Ri},o={[ft.included]:"included",[ft.excluded]:"excluded",[ft.partial]:"partially included"};let c=X(),u=X(),w=X();Me(()=>_(t()),()=>{var N;g(u,(N=t()).type===xt.folder&&N.inclusionState!==ft.excluded?N.expanded?"chevron-down":"chevron-right":N.type===xt.folder?"folder":"file")}),Me(()=>(_(t()),ft),()=>{g(c,t().type===xt.folder&&t().inclusionState!==ft.excluded)}),Me(()=>(_(t()),xt),()=>{g(w,t().type===xt.folder&&t().expanded&&t().children&&t().children.length>0?t():null)}),it(),We();var D=Ii(),A=h(D),ke=ls(()=>Bs("Enter",l));let ye;var x=h(A);aa(x,{get icon(){return n(u)}});var P=p(x,2),m=h(P),U=p(P,2),j=N=>{ve(N,{size:1,class:"file-count",children:(S,E)=>{var B=Z();we(O=>Ie(B,O),[()=>(_(t()),f(()=>t().trackedFileCount.toLocaleString()))],Se),d(S,B)},$$slots:{default:!0}})};K(U,N=>{_(t()),_(xt),_(ft),f(()=>t().type===xt.folder&&t().inclusionState!==ft.excluded&&typeof t().trackedFileCount=="number")&&N(j)});var b=p(U,2),I=p(A,2),z=N=>{var S=Ei();at(S,5,()=>(n(w),f(()=>n(w).children)),E=>Ut.fileIdToString(E.fileId),(E,B)=>{var O=tt(),y=$e(O);const k=Se(()=>a()+1);ma(y,{get data(){return n(B)},get wsContextModel(){return s()},get indentLevel(){return n(k)}}),d(E,O)}),d(N,S)};K(I,N=>{n(w)&&N(z)}),we(N=>{ye=_t(A,1,"tree-item svelte-sympus",null,ye,N),Kt(A,"title",(_(t()),f(()=>t().reason))),Kt(A,"aria-expanded",(_(t()),_(xt),f(()=>t().type===xt.folder&&t().expanded))),Kt(A,"aria-level",a()),ba(A,`padding-left: ${10*a()+20}px;`),Ie(m,(_(t()),f(()=>t().name))),Kt(b,"src",(_(t()),f(()=>i[t().inclusionState]))),Kt(b,"alt",(_(t()),f(()=>o[t().inclusionState])))},[()=>({"included-folder":n(c)})],Se),ut("click",A,l),ut("keyup",A,function(...N){var S;(S=n(ke))==null||S.apply(this,N)}),d(r,D),Je()}var zi=v('<div class="files-container svelte-8hfqhl"></div>'),Oi=Ct('<svg width="15" height="15" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><rect width="16" height="16" transform="matrix(-1 0 0 -1 16 16)" fill="currentColor" fill-opacity="0.01"></rect><path fill-rule="evenodd" clip-rule="evenodd" d="M13.7075 11.7333C13.7075 12.8236 12.8236 13.7075 11.7333 13.7075C10.643 13.7075 9.75909 12.8236 9.75909 11.7333C9.75909 10.643 10.643 9.75909 11.7333 9.75909C12.8236 9.75909 13.7075 10.643 13.7075 11.7333ZM11.7333 14.6675C13.3538 14.6675 14.6675 13.3538 14.6675 11.7333C14.6675 10.1128 13.3538 8.79909 11.7333 8.79909C10.1128 8.79909 8.79909 10.1128 8.79909 11.7333C8.79909 13.3538 10.1128 14.6675 11.7333 14.6675ZM9.79161 4.26647L13.3333 2.30721V6.22571L9.79161 4.26647ZM13.1852 7.24088C13.6829 7.51617 14.2933 7.15625 14.2933 6.58752V1.9454C14.2933 1.37665 13.6829 1.01676 13.1852 1.29207L8.98946 3.61313C8.47582 3.89729 8.47582 4.63564 8.98946 4.9198L13.1852 7.24088ZM7.14663 6.39988C7.14663 6.81225 6.81233 7.14654 6.39996 7.14654H2.1333C1.72093 7.14654 1.38664 6.81225 1.38664 6.39988V2.13324C1.38664 1.72087 1.72093 1.38657 2.1333 1.38657H6.39996C6.81233 1.38657 7.14663 1.72087 7.14663 2.13324V6.39988ZM6.18663 6.18654V2.34657H2.34664V6.18654H6.18663ZM1.66056 13.6606C1.47314 13.848 1.47314 14.152 1.66056 14.3394C1.84797 14.5269 2.15186 14.5269 2.33938 14.3394L4.26664 12.4121L6.19388 14.3394C6.38133 14.5268 6.68525 14.5268 6.8727 14.3394C7.06015 14.1519 7.06015 13.848 6.8727 13.6606L4.94546 11.7333L6.8727 9.80608C7.06015 9.61863 7.06015 9.31471 6.8727 9.12726C6.68525 8.9398 6.38133 8.9398 6.19388 9.12726L4.26664 11.0545L2.33938 9.12722C2.15186 8.93978 1.84797 8.93978 1.66056 9.12722C1.47314 9.31468 1.47314 9.61861 1.66056 9.80605L3.58781 11.7333L1.66056 13.6606Z" fill="currentColor"></path></svg>');function Li(r){var e=Oi();d(r,e)}var Zi=v('<div class="icon-wrapper svelte-13uht7n"><!></div>'),Pi=v("<!> <!>",1),Fi=v('<div class="settings-card-body"><!></div>'),ji=v('<div><div class="settings-card-content svelte-13uht7n"><div class="settings-card-left svelte-13uht7n"><!></div> <div class="settings-card-right svelte-13uht7n"><!></div></div> <!></div>');function Tt(r,e){const t=$a(e),s=us(e,["children","$$slots","$$events","$$legacy"]),a=us(s,["class","icon","title","isClickable"]);Be(e,!1);const l=X(),i=X(),o=X();let c=C(e,"class",8,""),u=C(e,"icon",24,()=>{}),w=C(e,"title",24,()=>{}),D=C(e,"isClickable",8,!1);Me(()=>(n(l),n(i),_(a)),()=>{g(l,a.class),g(i,Sa(a,["class"]))}),Me(()=>(_(c()),n(l)),()=>{g(o,`settings-card ${c()} ${n(l)||""}`)}),it();var A=ji();Fs(A,z=>({role:"button",class:n(o),...n(i),[ka]:z}),[()=>({clickable:D()})],"svelte-13uht7n");var ke=h(A),ye=h(ke),x=h(ye),P=z=>{var N=Pi(),S=$e(N),E=y=>{var k=Zi(),ae=h(k);_r(ae,u,(L,M)=>{M(L,{})}),d(y,k)};K(S,y=>{u()&&y(E)});var B=p(S,2),O=y=>{ve(y,{color:"neutral",size:1,weight:"light",class:"card-title",children:(k,ae)=>{var L=Z();we(()=>Ie(L,w())),d(k,L)},$$slots:{default:!0}})};K(B,y=>{w()&&y(O)}),d(z,N)},m=z=>{var N=tt(),S=$e(N);yt(S,e,"header-left",{},null),d(z,N)};K(x,z=>{u()||w()?z(P):z(m,!1)});var U=p(ye,2),j=h(U);yt(j,e,"header-right",{},null);var b=p(ke,2),I=z=>{var N=Fi(),S=h(N);yt(S,e,"default",{},null),d(z,N)};K(b,z=>{f(()=>t.default)&&z(I)}),ut("click",A,function(z){Ta.call(this,e,z)}),d(r,A),Je()}var Di=v('<div class="context-list svelte-qsnirf"><div><!> <!></div> <div><div class="files-header svelte-qsnirf"><!> <!></div> <!></div></div>'),Ui=v('<div slot="header-right"><!></div>');function Vi(r,e){Be(e,!1);const[t,s]=Mt(),a=()=>Xe(i,"$wsContextModel",t),l=X();let i=new Ut(et,new tn(et.postMessage)),o=X(),c=X();Me(()=>a(),()=>{g(o,a().sourceFolders.sort((u,w)=>u.isWorkspaceFolder!==w.isWorkspaceFolder?u.isWorkspaceFolder?-1:1:u.fileId.folderRoot.localeCompare(w.fileId.folderRoot)))}),Me(()=>a(),()=>{g(c,a().syncStatus)}),Me(()=>n(o),()=>{g(l,n(o).reduce((u,w)=>u+(w.trackedFileCount??0),0))}),it(),We(),ut("message",rr,function(...u){var w;(w=i.handleMessageFromExtension)==null||w.apply(this,u)}),Tt(r,{get icon(){return Li},title:"Context",$$events:{contextmenu:u=>u.preventDefault()},children:(u,w)=>{var D=Di(),A=h(D),ke=h(A);ve(ke,{size:1,weight:"medium",class:"context-section-header",children:(U,j)=>{var b=Z("SOURCE FOLDERS");d(U,b)},$$slots:{default:!0}}),function(U,j){Be(j,!1);let b=C(j,"folders",24,()=>[]),I=C(j,"onAddMore",8),z=C(j,"onRemove",8);We();var N=Ai(),S=h(N);at(S,1,b,y=>Ut.fileIdToString(y.fileId),(y,k)=>{var ae=Mi();let L;var M=h(ae),se=be=>{var Fe=ls(()=>Bs("Enter",()=>z()(n(k).fileId.folderRoot)));Qt(be,{title:"Remove source folder from Augment context",variant:"ghost",color:"neutral",size:1,class:"source-folder-v-adjust",$$events:{click:()=>z()(n(k).fileId.folderRoot),keyup(...R){var te;(te=n(Fe))==null||te.apply(this,R)}},children:(R,te)=>{Qa(R)},$$slots:{default:!0}})};K(M,be=>{n(k),f(()=>!n(k).isWorkspaceFolder)&&be(se)});var Y=p(M,2);const T=Se(()=>(n(k),f(()=>(be=>be.isWorkspaceFolder?"root-folder":"folder")(n(k)))));aa(Y,{class:"source-folder-v-adjust",get icon(){return n(T)}});var le=p(Y,2),Oe=h(le),Ne=p(Oe),me=h(Ne),F=p(le,2),Ce=be=>{ve(be,{size:1,class:"file-count",children:(Fe,R)=>{var te=Z();we(H=>Ie(te,H),[()=>(n(k),f(()=>n(k).trackedFileCount.toLocaleString()))],Se),d(Fe,te)},$$slots:{default:!0}})};K(F,be=>{n(k),f(()=>n(k).trackedFileCount)&&be(Ce)}),we(be=>{L=_t(ae,1,"item svelte-1skknri",null,L,be),Ie(Oe,`${n(k),f(()=>n(k).name)??""} `),Ie(me,(n(k),f(()=>n(k).isPending?"(pending)":n(k).fileId.folderRoot)))},[()=>({"workspace-folder":n(k).isWorkspaceFolder})],Se),d(y,ae)});var E=p(S,2),B=ls(()=>Bs("Enter",I())),O=h(E);hs(O,{}),ut("keyup",E,function(...y){var k;(k=n(B))==null||k.apply(this,y)}),ut("click",E,function(...y){var k;(k=I())==null||k.apply(this,y)}),d(U,N),Je()}(p(ke,2),{get folders(){return n(o)},onRemove:U=>i.removeSourceFolder(U),onAddMore:()=>i.addMoreSourceFolders()});var ye=p(A,2),x=h(ye),P=h(x);ve(P,{size:1,weight:"medium",class:"context-section-header",children:(U,j)=>{var b=Z("FILES");d(U,b)},$$slots:{default:!0}});var m=p(P,2);ve(m,{size:1,class:"file-count",children:(U,j)=>{var b=Z();we(I=>Ie(b,I),[()=>(n(l),f(()=>n(l).toLocaleString()))],Se),d(U,b)},$$slots:{default:!0}}),function(U,j){Be(j,!1);const[b,I]=Mt(),z=()=>Xe(N(),"$wsContextModel",b);let N=C(j,"wsContextModel",8),S=X();Me(()=>z(),()=>{g(S,z().sourceTree)}),it(),We();var E=zi();at(E,5,()=>n(S),B=>Ut.fileIdToString(B.fileId),(B,O)=>{ma(B,{get wsContextModel(){return N()},get data(){return n(O)},indentLevel:0})}),d(U,E),Je(),I()}(p(x,2),{get wsContextModel(){return i}}),d(u,D)},$$slots:{default:!0,"header-right":(u,w)=>{var D=Ui(),A=h(D),ke=ye=>{var x=ls(()=>Bs("Enter",()=>i.requestRefresh()));Qt(ye,{title:"Refresh",variant:"ghost-block",color:"neutral",size:1,$$events:{click:()=>i.requestRefresh(),keyup(...P){var m;(m=n(x))==null||m.apply(this,P)}},children:(P,m)=>{Fa(P)},$$slots:{default:!0}})};K(A,ye=>{n(c),_(gr),f(()=>n(c)===gr.done)&&ye(ke)}),d(u,D)}}}),Je(),s()}function kr(r){return function(e){switch(typeof e){case"object":return e!=null;case"function":return!0;default:return!1}}(r)&&"name"in r}function Br(r){return kr(r)&&"component"in r}var qi=Ct('<svg width="16" height="15" viewBox="0 0 16 15" xmlns="http://www.w3.org/2000/svg"><path d="M5.5 1.75V3H10.5V1.75C10.5 1.625 10.375 1.5 10.25 1.5H5.75C5.59375 1.5 5.5 1.625 5.5 1.75ZM4 3V1.75C4 0.8125 4.78125 0 5.75 0H10.25C11.1875 0 12 0.8125 12 1.75V3H14C15.0938 3 16 3.90625 16 5V8.75V13C16 14.125 15.0938 15 14 15H2C0.875 15 0 14.125 0 13V8.75V5C0 3.90625 0.875 3 2 3H4ZM1.5 9.5V13C1.5 13.2812 1.71875 13.5 2 13.5H14C14.25 13.5 14.5 13.2812 14.5 13V9.5H10V10C10 10.5625 9.53125 11 9 11H7C6.4375 11 6 10.5625 6 10V9.5H1.5ZM6 8H10H14.5V5C14.5 4.75 14.25 4.5 14 4.5H11.25H4.75H2C1.71875 4.5 1.5 4.75 1.5 5V8H6Z" fill="currentColor"></path></svg>');function Jr(r){var e=qi();d(r,e)}var Hi=v('<div class="c-navigation__content-header svelte-z0ijuz"> </div>'),Bi=v('<div class="c-navigation__content-description svelte-z0ijuz"> </div>'),Ji=v('<!> <!> <div class="c-navigation__content-container svelte-z0ijuz"><!></div>',1),Gi=v('<div class="c-navigation__content svelte-z0ijuz"><!> <div><!></div></div>');function Gr(r,e){Be(e,!1);let t=C(e,"item",8);We();var s=Gi(),a=h(s);yt(a,e,"header",{},null);var l=p(a,2),i=h(l),o=c=>{var u=Ji(),w=$e(u);ve(w,{size:4,weight:"medium",color:"neutral",children:(x,P)=>{var m=Hi(),U=h(m);we(()=>Ie(U,(_(t()),f(()=>{var j;return(j=t())==null?void 0:j.name})))),d(x,m)},$$slots:{default:!0}});var D=p(w,2),A=x=>{ve(x,{color:"secondary",size:1,weight:"light",children:(P,m)=>{var U=Bi(),j=h(U);we(()=>Ie(j,(_(t()),f(()=>{var b;return(b=t())==null?void 0:b.description})))),d(P,U)},$$slots:{default:!0}})};K(D,x=>{_(t()),f(()=>{var P;return(P=t())==null?void 0:P.description})&&x(A)});var ke=p(D,2),ye=h(ke);yt(ye,e,"content",{get item(){return t()}},null),d(c,u)};K(i,c=>{t()!=null&&c(o)}),we(()=>Kt(s,"id",(_(t()),f(()=>{var c;return(c=t())==null?void 0:c.id})))),d(r,s),Je()}function ws(r,e,t,s,a,l){return a!==void 0?{name:r,description:e,icon:t,id:s,component:a,props:l}:{name:r,description:e,icon:t,id:s}}var Wi=v('<div class="c-navigation__head svelte-n5ccbo"><!> <!></div>'),Ki=v('<span class="c-navigation__head-icon"><!></span> ',1),Yi=v("<button><!></button>"),Xi=v('<div class="c-navigation__group"><!> <div class="c-navigation__items svelte-n5ccbo"></div></div>'),Qi=v('<nav class="c-navigation__nav svelte-n5ccbo" slot="left"><!></nav>'),eo=v('<span class="c-navigation__head-icon"><!></span> <span> </span>',1),to=v("<span><!></span>"),so=v('<div class="c-navigation__head svelte-n5ccbo"><!></div> <!>',1),ro=v('<div class="c-navigation__flat svelte-n5ccbo"><!> <!></div>'),ao=v("<div><!></div>");function no(r,e){Be(e,!1);let t=C(e,"group",8,"Workspace Settings"),s=C(e,"items",24,()=>[]),a=C(e,"item",28,()=>{}),l=C(e,"mode",8,"tree"),i=C(e,"selectedId",28,()=>{}),o=C(e,"onNavigationChangeItem",8,x=>{}),c=C(e,"showButton",8,!0),u=C(e,"class",8,""),w=X(new Map);Me(()=>(_(i()),_(a()),_(s())),()=>{var x;i()?a(s().find(P=>(P==null?void 0:P.id)===i())):i((x=a())==null?void 0:x.id)}),Me(()=>(_(s()),_(t())),()=>{g(w,s().reduce((x,P)=>{if(!P)return x;const m=P.group??t(),U=x.get(m)??[];return U.push(P),x.set(m,U),x},new Map))}),Me(()=>(_(a()),_(s())),()=>{a()||a(s()[0])}),Me(()=>(_(o()),_(i())),()=>{o()(i())}),it(),We();var D=ao(),A=h(D),ke=x=>{an(x,{initialWidth:200,expandedMinWidth:150,columnLayoutThreshold:0,get showButton(){return c()},minimized:!1,$$slots:{left:(P,m)=>{var U=Qi(),j=h(U);sn(j,i,b=>{var I=tt(),z=$e(I);at(z,1,()=>n(w),Dt,(N,S)=>{var E=ls(()=>Er(n(S),2));let B=()=>n(E)[0];var O=Xi(),y=h(O);yt(y,e,"group",{get label(){return B()},get mode(){return l()}},ae=>{var L=Wi(),M=h(L);Jr(M);var se=p(M,2);ve(se,{size:2,color:"primary",children:(Y,T)=>{var le=Z();we(()=>Ie(le,B())),d(Y,le)},$$slots:{default:!0}}),d(ae,L)});var k=p(y,2);at(k,5,()=>n(E)[1],Dt,(ae,L)=>{var M=Yi();let se;var Y=h(M);ve(Y,{size:2,weight:"regular",color:"primary",children:(T,le)=>{var Oe=Ki(),Ne=$e(Oe),me=h(Ne);_r(me,()=>n(L).icon,(Ce,be)=>{be(Ce,{})});var F=p(Ne);we(()=>Ie(F,` ${n(L),f(()=>n(L).name)??""}`)),d(T,Oe)},$$slots:{default:!0}}),we(T=>se=_t(M,1,"c-navigation__item svelte-n5ccbo",null,se,T),[()=>({"is-active":n(L).id===i()})],Se),ut("click",M,()=>{return T=n(L),a(T),void i(T==null?void 0:T.id);var T}),d(ae,M)}),d(N,O)}),d(b,I)}),d(P,U)},right:(P,m)=>{Gr(P,{get item(){return a()},slot:"right",$$slots:{header:(U,j)=>{var b=tt(),I=$e(b);yt(I,e,"header",{get item(){return a()},get selectedId(){return i()}},null),d(U,b)},content:(U,j)=>{var b=tt(),I=$e(b);yt(I,e,"content",{get item(){return a()},get isSelected(){return _(a()),_(i()),f(()=>{var z;return((z=a())==null?void 0:z.id)===i()})}},z=>{var N=tt(),S=$e(N),E=B=>{var O=tt(),y=$e(O);_r(y,()=>a().component,(k,ae)=>{ae(k,xa(()=>a().props))}),d(B,O)};K(S,B=>{_(Br),_(a()),_(l()),_(i()),f(()=>{return Br(a())&&(O=a(),y=l(),k=i(),y!=="tree"||(O==null?void 0:O.id)===k);var O,y,k})&&B(E)}),d(z,N)}),d(U,b)}}})}}})},ye=x=>{var P=ro(),m=h(P);yt(m,e,"header",{get item(){return a()}},null);var U=p(m,2);at(U,1,()=>n(w),Dt,(j,b)=>{var I=ls(()=>Er(n(b),2));let z=()=>n(I)[0];var N=so(),S=$e(N),E=h(S);yt(E,e,"group",{get label(){return z()},get mode(){return l()}},O=>{ve(O,{color:"secondary",size:2,weight:"medium",children:(y,k)=>{var ae=eo(),L=$e(ae);Jr(h(L));var M=p(L,2),se=h(M);we(()=>Ie(se,z())),d(y,ae)},$$slots:{default:!0}})});var B=p(S,2);at(B,1,()=>n(I)[1],Dt,(O,y)=>{var k=to();Gr(h(k),{get item(){return n(y)},$$slots:{content:(ae,L)=>{var M=tt(),se=$e(M);yt(se,e,"content",{get item(){return n(y)}},null),d(ae,M)}}}),Na(k,(ae,L)=>function(M,se){let Y;function T({scrollTo:le,delay:Oe,options:Ne}){clearTimeout(Y),le&&(Y=setTimeout(()=>{M.scrollIntoView(Ne)},Oe))}return T(se),{update:T,destroy(){clearTimeout(Y)}}}(ae,L),()=>({scrollTo:l()==="flat"&&n(y).id===i(),delay:300,options:{behavior:"smooth"}})),d(O,k)}),d(j,N)}),d(x,P)};K(A,x=>{l()==="tree"?x(ke):x(ye,!1)}),we(()=>_t(D,1,`c-navigation c-navigation--mode__${l()??""} ${u()??""}`,"svelte-n5ccbo")),d(r,D),Je()}var io=Ct('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M3.13281 0.886719L5.97656 3.07422C6.14062 3.21094 6.25 3.40234 6.25 3.59375V5.07031L9.23047 8.05078C10.0234 7.66797 11.0078 7.80469 11.6641 8.46094L14.7266 11.5234C15.082 11.8516 15.082 12.4258 14.7266 12.7539L12.9766 14.5039C12.6484 14.8594 12.0742 14.8594 11.7461 14.5039L8.68359 11.4414C8.02734 10.7852 7.89062 9.77344 8.30078 8.98047L5.32031 6H3.81641C3.625 6 3.43359 5.91797 3.29688 5.75391L1.10938 2.91016C0.917969 2.63672 0.945312 2.28125 1.19141 2.03516L2.28516 0.941406C2.50391 0.722656 2.88672 0.695312 3.13281 0.886719ZM1.62891 11.0586L5.375 7.3125L6.30469 8.24219L2.55859 11.9883C2.39453 12.1523 2.3125 12.3711 2.3125 12.5898C2.3125 13.0547 2.69531 13.4375 3.16016 13.4375C3.37891 13.4375 3.59766 13.3555 3.76172 13.1914L7.17969 9.77344C7.15234 10.293 7.26172 10.8125 7.50781 11.3047L4.69141 14.1211C4.28125 14.5312 3.73438 14.75 3.16016 14.75C1.95703 14.75 1 13.793 1 12.5898C1 12.0156 1.21875 11.4688 1.62891 11.0586ZM13.6602 5.23438L12.9766 5.94531C12.6484 6.27344 12.2109 6.46484 11.7461 6.46484H11.0625C10.0781 6.46484 9.3125 5.67188 9.3125 4.71484V4.00391C9.3125 3.53906 9.47656 3.10156 9.80469 2.77344L10.5156 2.08984C8.875 2.14453 7.5625 3.48438 7.5625 5.125V5.15234L7.125 4.71484V3.59375C7.125 3.32031 7.04297 3.04688 6.90625 2.82812C7.67188 1.59766 9.03906 0.75 10.625 0.75C11.2812 0.75 11.9375 0.914062 12.5117 1.1875C12.7578 1.32422 12.7852 1.65234 12.5938 1.84375L10.7344 3.70312C10.6523 3.78516 10.625 3.89453 10.625 4.00391V4.6875C10.625 4.93359 10.8164 5.125 11.0625 5.125L11.7461 5.15234C11.8555 5.15234 11.9648 5.09766 12.0469 5.01562L13.9062 3.15625C14.0977 2.96484 14.4258 2.99219 14.5625 3.23828C14.8359 3.8125 15 4.46875 15 5.15234C15 6.60156 14.2617 7.91406 13.1406 8.70703L12.293 7.83203C12.2656 7.80469 12.2383 7.77734 12.2109 7.75C13.0586 7.23047 13.6328 6.30078 13.6602 5.23438Z" fill="currentColor"></path></svg>');function oo(r){var e=io();d(r,e)}var lo=Ct('<svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg"><path d="M6.25 10.8125H12.375C12.5938 10.8125 12.8125 10.6211 12.8125 10.375V4.25H11.5C11.0078 4.25 10.625 3.86719 10.625 3.375V2.0625H6.25C6.00391 2.0625 5.8125 2.28125 5.8125 2.5V10.375C5.8125 10.6211 6.00391 10.8125 6.25 10.8125ZM12.375 12.125H6.25C5.26562 12.125 4.5 11.3594 4.5 10.375V2.5C4.5 1.54297 5.26562 0.75 6.25 0.75H10.7617C11.2266 0.75 11.6641 0.941406 11.9922 1.26953L13.6055 2.88281C13.9336 3.21094 14.125 3.64844 14.125 4.11328V10.375C14.125 11.3594 13.332 12.125 12.375 12.125ZM2.53125 3.375C2.88672 3.375 3.1875 3.67578 3.1875 4.03125V11.0312C3.1875 12.3711 4.25391 13.4375 5.59375 13.4375H10.8438C11.1992 13.4375 11.5 13.7383 11.5 14.0938C11.5 14.4766 11.1992 14.75 10.8438 14.75H5.59375C3.51562 14.75 1.875 13.1094 1.875 11.0312V4.03125C1.875 3.67578 2.14844 3.375 2.53125 3.375Z" fill="currentColor"></path></svg>');function co(r){var e=lo();d(r,e)}var uo=Ct("<svg><!></svg>");function ga(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=uo();Fs(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 17 16",...t}));var a=h(s);ar(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M3.552 7.158a.57.57 0 0 1 .804 0l.702.702L6.23 6.688c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804L5.862 8.664l1.626 1.626 1.173-1.172c.2-.2.511-.22.733-.06l.071.06a.57.57 0 0 1 0 .804l-1.173 1.172.703.703c.2.2.22.51.06.733l-.06.07a.57.57 0 0 1-.804 0l-.041-.039-.812.813a3.226 3.226 0 0 1-4.043.421l-.08-.054-.959.96c-.2.2-.511.22-.733.06l-.071-.06a.57.57 0 0 1 0-.804l.96-.96-.054-.079a3.226 3.226 0 0 1 .294-3.91l.127-.133.811-.813-.038-.04a.57.57 0 0 1-.06-.734zm3.759-3.759a.57.57 0 0 1 .804 0l.038.04.815-.813a3.226 3.226 0 0 1 4.043-.421l.078.054.96-.96c.2-.2.511-.22.734-.06l.07.06a.57.57 0 0 1 0 .804l-.96.96.055.079a3.226 3.226 0 0 1-.295 3.91l-.126.133-.814.813.04.04c.201.2.221.511.06.734l-.06.07a.57.57 0 0 1-.804 0L7.31 4.204a.57.57 0 0 1 0-.805m2.39-.04-.884.884 3.093 3.093.884-.884A2.186 2.186 0 1 0 9.7 3.359M4.396 8.664l-.884.884a2.186 2.186 0 1 0 3.092 3.093l.884-.884z"/>',!0),d(r,s)}var ho=v('<div class="connect-button-spinner svelte-js5lik"><!></div> <span>Cancel</span>',1),po=v("<span>Connect</span>"),vo=v('<div class="connect-button-content svelte-js5lik"><!></div>'),mo=v('<div class="status-controls svelte-js5lik"><div class="icon-container svelte-js5lik"><div class="connection-status svelte-js5lik"><div><!></div></div> <!></div></div>'),go=v('<div slot="header-right"><!></div>'),fo=v("<div> </div>"),yo=v('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>'),_o=v('<div class="loading-container svelte-2bsejd"><!> <!></div>'),Co=v('<div class="category-content"><!></div>'),wo=v('<div class="category"><div class="category-heading"><!></div> <!></div>');const fa="extensionClient",ya="mcpServerModel";function Nr(){const r=ds(ya);if(!r)throw new Error("MCPServerModel context not found. Make sure setMCPServerModelContext() was called in a parent component.");return r}var bo=v('<div class="connect-button-spinner svelte-e3a21z"><!></div> <span>Cancel</span>',1),$o=v("<span>Connect</span>"),So=v('<div class="connect-button-content svelte-e3a21z"><!></div>'),ko=v('<div class="status-controls svelte-e3a21z"><div><!></div> <!></div>'),xo=v('<div slot="header-right"><!></div>'),Mo=v("<div> </div>"),Ao=v('<div class="config-wrapper" role="group" aria-label="Connection status controls"><!> <!></div>');function To(r,e){Be(e,!1);let t=C(e,"config",12),s=C(e,"mcpTool",8);const a=Nr(),l=function(){const x=ds(fa);if(!x)throw new Error("ExtensionClient context not found. Make sure setExtensionClientContext() was called in a parent component.");return x}();async function i(){if(n(c))return w&&(clearTimeout(w),w=null),void g(c,!1);l.startRemoteMCPAuth(t().name),g(c,!0);const x=new Promise(P=>{w=setTimeout(()=>{P(),w=null},6e4)});await Promise.race([x]),g(c,!1)}async function o(){var x;await l.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${t().displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&(s()&&a.deleteServer((x=s())==null?void 0:x.id),g(c,!1))}let c=X(!1),u=X(!1),w=null;Me(()=>(_(t()),Zr),()=>{t(Zr(t()))}),Me(()=>_(s()),()=>{t(t().isConfigured=!!s(),!0)}),it(),We();var D=Ao(),A=h(D);Tt(A,{get icon(){return _(t()),f(()=>t().icon)},get title(){return _(t()),f(()=>t().displayName)},$$slots:{"header-right":(x,P)=>{var m=xo(),U=h(m),j=I=>{const z=Se(()=>n(c)?"neutral":"accent");He(I,{variant:"ghost-block",get color(){return n(z)},size:1,$$events:{click:i},children:(N,S)=>{var E=So(),B=h(E),O=k=>{var ae=bo(),L=$e(ae),M=h(L);mr(M,{size:1,useCurrentColor:!0}),d(k,ae)},y=k=>{var ae=$o();d(k,ae)};K(B,k=>{n(c)?k(O):k(y,!1)}),d(N,E)},$$slots:{default:!0}})},b=(I,z)=>{var N=S=>{var E=ko(),B=h(E);let O;var y=h(B);const k=Se(()=>(_(ps),f(()=>[ps.Hover])));Lt(y,{get triggerOn(){return n(k)},content:"Revoke Access",children:(L,M)=>{Qt(L,{color:"neutral",variant:"ghost",size:1,$$events:{click:o},children:(se,Y)=>{ga(se,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var ae=p(B,2);Mr.Root(ae,{color:"success",size:1,variant:"soft",children:(L,M)=>{var se=Z("Connected");d(L,se)},$$slots:{default:!0}}),we(L=>O=_t(B,1,"disconnect-button svelte-e3a21z",null,O,L),[()=>({active:n(u)})],Se),d(S,E)};K(I,S=>{_(t()),f(()=>t().isConfigured)&&S(N)},z)};K(U,I=>{_(t()),f(()=>!t().isConfigured)?I(j):I(b,!1)}),d(x,m)}}});var ke=p(A,2),ye=x=>{var P=Mo(),m=h(P);we(()=>{_t(P,1,`status-message ${_(t()),f(()=>t().statusType)??""}`,"svelte-e3a21z"),Ie(m,(_(t()),f(()=>t().statusMessage)))}),d(x,P)};K(ke,x=>{_(t()),f(()=>t().showStatus)&&x(ye)}),ut("mouseenter",D,()=>g(u,!0)),ut("mouseleave",D,()=>g(u,!1)),d(r,D),Je()}var No=v('<div class="tool-category-list svelte-on3wl5"><!> <!></div>'),Ro=v("<div><!></div>");function Eo(r,e){Be(e,!1);const[t,s]=Mt(),a=()=>Xe(x,"$allServers",t),l=()=>Xe(ye,"$pretendNativeToolDefs",t),i=X();let o=C(e,"title",8),c=C(e,"tools",24,()=>[]),u=C(e,"onAuthenticate",8),w=C(e,"onRevokeAccess",8),D=C(e,"onToolApprovalConfigChange",8,()=>{});const A=ds(ks.key),ke=Nr(),ye=A.getPretendNativeToolDefs(),x=ke.getServers();Me(()=>a(),()=>{g(i,A.getEnableNativeRemoteMcp()?Qr(a()):[])}),it(),We();var P=Ro(),m=h(P);const U=Se(()=>(_(c()),f(()=>c().length===0)));(function(j,b){let I=C(b,"title",8),z=C(b,"loading",8,!1);var N=wo(),S=h(N),E=h(S);ve(E,{size:1,color:"secondary",weight:"regular",children:(k,ae)=>{var L=Z();we(()=>Ie(L,I())),d(k,L)},$$slots:{default:!0}});var B=p(S,2),O=k=>{var ae=_o(),L=h(ae);mr(L,{size:1});var M=p(L,2);ve(M,{size:1,color:"secondary",children:(se,Y)=>{var T=Z("Loading...");d(se,T)},$$slots:{default:!0}}),d(k,ae)},y=k=>{var ae=Co(),L=h(ae);yt(L,b,"default",{},null),d(k,ae)};K(B,k=>{z()?k(O):k(y,!1)}),d(j,N)})(m,{get title(){return o()},get loading(){return n(U)},children:(j,b)=>{var I=No(),z=h(I);at(z,1,c,S=>S.name,(S,E)=>{(function(B,O){Be(O,!1);let y=C(O,"config",8),k=C(O,"onAuthenticate",8),ae=C(O,"onRevokeAccess",8);const L=()=>{};let M=X(!1),se=X(null),Y=X(!1);function T(){if(n(M))g(M,!1),n(se)&&(clearTimeout(n(se)),g(se,null));else{g(M,!0);const F=y().authUrl||"";k()(F),g(se,setTimeout(()=>{g(M,!1),g(se,null)},6e4))}}Me(()=>(_(y()),n(M),n(se)),()=>{y().isConfigured&&n(M)&&(g(M,!1),n(se)&&(clearTimeout(n(se)),g(se,null)))}),it(),We();var le=yo(),Oe=h(le);Tt(Oe,{get icon(){return _(y()),f(()=>y().icon)},get title(){return _(y()),f(()=>y().displayName)},$$slots:{"header-right":(F,Ce)=>{var be=go(),Fe=h(be),R=H=>{const re=Se(()=>n(M)?"neutral":"accent");He(H,{variant:"ghost-block",get color(){return n(re)},size:1,$$events:{click:T},children:(de,Q)=>{var ne=vo(),ge=h(ne),ce=ue=>{var q=ho(),pe=$e(q),J=h(pe);mr(J,{size:1,useCurrentColor:!0}),d(ue,q)},ee=ue=>{var q=po();d(ue,q)};K(ge,ue=>{n(M)?ue(ce):ue(ee,!1)}),d(de,ne)},$$slots:{default:!0}})},te=(H,re)=>{var de=Q=>{var ne=mo(),ge=h(ne),ce=h(ge),ee=h(ce);let ue;var q=h(ee);const pe=Se(()=>(_(ps),f(()=>[ps.Hover])));Lt(q,{get triggerOn(){return n(pe)},content:"Revoke Access",children:(oe,G)=>{Qt(oe,{color:"neutral",variant:"ghost",size:1,$$events:{click:()=>ae()(y())},children:(he,xe)=>{ga(he,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var J=p(ce,2);Mr.Root(J,{color:"success",size:1,variant:"soft",children:(oe,G)=>{var he=Z("Connected");d(oe,he)},$$slots:{default:!0}}),we(oe=>ue=_t(ee,1,"icon-button-wrapper svelte-js5lik",null,ue,oe),[()=>({active:n(Y)})],Se),d(Q,ne)};K(H,Q=>{_(y()),f(()=>y().isConfigured)&&Q(de)},re)};K(Fe,H=>{_(y()),f(()=>!y().isConfigured&&y().authUrl)?H(R):H(te,!1)}),d(F,be)}}});var Ne=p(Oe,2),me=F=>{var Ce=fo(),be=h(Ce);we(()=>{_t(Ce,1,`status-message ${_(y()),f(()=>y().statusType)??""}`,"svelte-js5lik"),Ie(be,(_(y()),f(()=>y().statusMessage)))}),d(F,Ce)};K(Ne,F=>{_(y()),f(()=>y().showStatus)&&F(me)}),ut("mouseenter",le,()=>g(Y,!0)),ut("mouseleave",le,()=>g(Y,!1)),d(B,le),ia(O,"onToolApprovalConfigChange",L),Je({onToolApprovalConfigChange:L})})(S,{get config(){return n(E)},get onAuthenticate(){return u()},get onRevokeAccess(){return w()},onToolApprovalConfigChange:D()})});var N=p(z,2);at(N,1,l,S=>S.name,(S,E)=>{const B=Se(()=>(n(i),n(E),f(()=>n(i).find(O=>O.name===n(E).name))));To(S,{get mcpTool(){return n(B)},get config(){return n(E)}})}),d(j,I)},$$slots:{default:!0}}),d(r,P),Je(),s()}var Io=v('<tr class="env-var-row svelte-1mazg1z"><td class="name-cell svelte-1mazg1z"><!></td><td class="value-cell svelte-1mazg1z"><!></td><td class="action-cell svelte-1mazg1z"><!></td></tr>'),zo=v('<!> <table class="env-vars-table svelte-1mazg1z"><tbody><!></tbody></table> <div class="new-var-button-container svelte-1mazg1z"><!></div>',1);function Oo(r,e){Be(e,!1);let t=C(e,"handleEnterEditMode",8),s=C(e,"envVarEntries",28,()=>[]);We();var a=zo(),l=$e(a);ve(l,{size:1,weight:"medium",children:(A,ke)=>{var ye=Z("Environment Variables");d(A,ye)},$$slots:{default:!0}});var i=p(l,2),o=h(i),c=h(o),u=A=>{var ke=tt(),ye=$e(ke);at(ye,1,s,x=>x.id,(x,P,m)=>{var U=Io(),j=h(U),b=h(j);Yt(b,{size:1,placeholder:"Name",class:"full-width",get value(){return n(P).key},set value(E){n(P).key=E,Ir(()=>s())},$$events:{focus(...E){var B;(B=t())==null||B.apply(this,E)},change:()=>function(E,B){const O=s().findIndex(y=>y.id===E);O!==-1&&(s(s()[O].key=B,!0),s(s()))}(n(P).id,n(P).key)},$$legacy:!0});var I=p(j),z=h(I);Yt(z,{size:1,placeholder:"Value",class:"full-width",get value(){return n(P).value},set value(E){n(P).value=E,Ir(()=>s())},$$events:{focus(...E){var B;(B=t())==null||B.apply(this,E)},change:()=>function(E,B){const O=s().findIndex(y=>y.id===E);O!==-1&&(s(s()[O].value=B,!0),s(s()))}(n(P).id,n(P).value)},$$legacy:!0});var N=p(I),S=h(N);Lt(S,{content:"Remove",children:(E,B)=>{He(E,{variant:"ghost",color:"neutral",type:"button",size:1,$$events:{focus(...O){var y;(y=t())==null||y.apply(this,O)},click:()=>{return O=n(P).id,t()(),void s(s().filter(y=>y.id!==O));var O}},$$slots:{iconLeft:(O,y)=>{ea(O,{slot:"iconLeft"})}}})},$$slots:{default:!0}}),d(x,U)}),d(A,ke)};K(c,A=>{_(s()),f(()=>s().length>0)&&A(u)});var w=p(i,2),D=h(w);He(D,{size:1,variant:"soft",color:"neutral",type:"button",$$events:{click:function(){t()(),s([...s(),{id:crypto.randomUUID(),key:"",value:""}])}},children:(A,ke)=>{var ye=Z("Variable");d(A,ye)},$$slots:{default:!0,iconLeft:(A,ke)=>{hs(A,{slot:"iconLeft"})}}}),d(r,a),Je()}var Lo=v("<div></div>"),Zo=v(" <!>",1),Po=v('<div class="server-name svelte-igdbzh"><!></div>'),Fo=v('<div slot="header-left" class="l-header svelte-igdbzh"><!> <!> <!> <div class="command-text svelte-igdbzh"><!></div></div>'),jo=v('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),Do=v('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),Uo=v('<div class="status-controls-button svelte-igdbzh"><!> <!></div>'),Vo=v("<!> <!> <!>",1),qo=v("<!> <!>",1),Ho=v('<div class="server-actions svelte-igdbzh" slot="header-right"><div class="status-controls svelte-igdbzh"><!> <!></div></div>'),Bo=v('<div class="c-tool-item svelte-igdbzh"><div class="c-tool-info svelte-igdbzh"><div class="tool-status svelte-igdbzh"><div></div> <!></div> <div class="c-tool-description svelte-igdbzh"><!></div></div></div>'),Jo=v('<div slot="footer"></div>'),Go=v('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div> <div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>',1),Wo=v('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!> <div class="connection-type-buttons svelte-igdbzh"><!> <!></div></div></div>'),Ko=v('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>'),Yo=v('<div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div>'),Xo=v('<!> <div class="form-row svelte-igdbzh"><div class="input-field svelte-igdbzh"><!></div></div> <!>',1),Qo=v('<form><div class="server-edit-form svelte-igdbzh"><div class="server-header svelte-igdbzh"><div class="server-title svelte-igdbzh"><div class="server-icon svelte-igdbzh"><!></div> <!></div></div> <!> <!> <div class="form-actions-row svelte-igdbzh"><div><!></div> <div class="form-actions svelte-igdbzh"><!> <!></div></div></div></form>');function Wr(r,e){var Ne;Be(e,!1);const t=X(),s=X(),a=X(),l=X(),i=X();let o=C(e,"server",8,null),c=C(e,"onDelete",8),u=C(e,"onAdd",8),w=C(e,"onSave",8),D=C(e,"onEdit",8),A=C(e,"onToggleDisableServer",8),ke=C(e,"onJSONImport",8),ye=C(e,"onCancel",8),x=C(e,"disabledText",24,()=>{}),P=C(e,"warningText",24,()=>{}),m=C(e,"mode",12,"view"),U=C(e,"mcpServerError",12,""),j=X(((Ne=o())==null?void 0:Ne.name)??""),b=X(bt(o())?"":os(o())?o().command:""),I=X(bt(o())?o().url:""),z=os(o())?o().env??{}:{},N=X(""),S=X(bt(o())?o().type:"http"),E=X([]);O();let B=X(!0);function O(){g(E,Object.entries(z).map(([me,F])=>({id:crypto.randomUUID(),key:me,value:F})))}let y=X(()=>{});function k(){o()&&m()==="view"&&(m("edit"),D()(o()),n(y)())}let ae=C(e,"busy",12,!1);function L({key:me,value:F}){return me.trim()&&F.trim()}async function M(){U(""),ae(!0);const me=n(E).filter(L);z=Object.fromEntries(me.map(({key:F,value:Ce})=>[F.trim(),Ce.trim()])),O();try{if(m()==="add"){const F={type:"stdio",name:n(j).trim(),command:n(b).trim(),arguments:"",useShellInterpolation:!0,env:Object.keys(z).length>0?z:void 0};await u()(F)}else if(m()==="addRemote"){const F={type:n(S),name:n(j).trim(),url:n(I).trim()};await u()(F)}else if(m()==="addJson"){try{JSON.parse(n(N))}catch(F){const Ce=F instanceof Error?F.message:String(F);throw new dt(`Invalid JSON format: ${Ce}`)}await ke()(n(N))}else if(m()==="edit"&&o()){if(bt(o())){const F={...o(),type:n(S),name:n(j).trim(),url:n(I).trim()};await w()(F)}else if(os(o())){const F={...o(),name:n(j).trim(),command:n(b).trim(),arguments:"",env:Object.keys(z).length>0?z:void 0};await w()(F)}}}catch(F){U(F instanceof dt?F.message:"Failed to save server"),console.warn(F)}finally{ae(!1)}}function se(){var me,F;ae(!1),U(""),(me=ye())==null||me(),g(N,""),g(j,((F=o())==null?void 0:F.name)??""),g(b,bt(o())?"":os(o())?o().command:""),g(I,bt(o())?o().url:""),z=os(o())&&o().env?{...o().env}:{},g(S,bt(o())?o().type:"http"),O()}Me(()=>_(o()),()=>{var me;g(t,((me=o())==null?void 0:me.tools)??[])}),Me(()=>(n(j),n(b)),()=>{n(j)&&n(b)&&U("")}),Me(()=>(_(m()),n(j),n(b),n(I)),()=>{g(s,!((m()!=="add"||n(j).trim()&&n(b).trim())&&(m()!=="addRemote"||n(j).trim()&&n(I).trim())))}),Me(()=>(_(m()),n(N)),()=>{g(a,m()==="addJson"&&!n(N).trim())}),Me(()=>(n(s),_(m()),n(a)),()=>{g(l,n(s)||m()==="view"||n(a))}),Me(()=>_(m()),()=>{g(i,(()=>{switch(m()){case"add":return"New MCP Server";case"addRemote":return"New Remote MCP Server";case"addJson":return"Import MCP Server";default:return"Edit MCP Server"}})())}),it(),We();var Y=tt(),T=$e(Y),le=me=>{na(me,{get collapsed(){return n(B)},set collapsed(F){g(B,F)},$$slots:{header:(F,Ce)=>{Tt(F,{slot:"header",$$slots:{"header-left":(be,Fe)=>{var R=Fo(),te=h(R),H=ee=>{Qt(ee,{size:1,variant:"ghost",$$events:{click:()=>g(B,!n(B))},children:(ue,q)=>{var pe=tt(),J=$e(pe),oe=he=>{Ga(he,{})},G=he=>{Ar(he,{})};K(J,he=>{n(B)?he(oe):he(G,!1)}),d(ue,pe)},$$slots:{default:!0}})};K(te,ee=>{n(t),f(()=>n(t).length>0)&&ee(H)});var re=p(te,2);const de=Se(()=>x()||P());Lt(re,{get content(){return n(de)},children:(ee,ue)=>{var q=Lo();let pe;we(J=>pe=_t(q,1,"c-dot svelte-igdbzh",null,pe,J),[()=>({"c-green":!x(),"c-warning":!x()&&!!P(),"c-red":!!x(),"c-disabled":o().disabled})],Se),d(ee,q)},$$slots:{default:!0}});var Q=p(re,2);Lt(Q,{get content(){return _(o()),f(()=>o().name)},side:"top",align:"start",children:(ee,ue)=>{var q=Po(),pe=h(q);ve(pe,{size:1,weight:"medium",children:(J,oe)=>{var G=Zo(),he=$e(G),xe=p(he),Re=Ee=>{var Ke=Z();we(()=>Ie(Ke,`(${n(t),f(()=>n(t).length)??""}) tools`)),d(Ee,Ke)};K(xe,Ee=>{n(t),f(()=>n(t).length>0)&&Ee(Re)}),we(()=>Ie(he,`${_(o()),f(()=>o().name)??""} `)),d(J,G)},$$slots:{default:!0}}),d(ee,q)},$$slots:{default:!0}});var ne=p(Q,2),ge=h(ne);const ce=Se(()=>(_(Hs),_(o()),f(()=>Hs(o()))));Lt(ge,{get content(){return n(ce)},side:"top",align:"start",children:(ee,ue)=>{ve(ee,{color:"secondary",size:1,weight:"regular",children:(q,pe)=>{var J=Z();we(oe=>Ie(J,oe),[()=>(_(Hs),_(o()),f(()=>Hs(o())))],Se),d(q,J)},$$slots:{default:!0}})},$$slots:{default:!0}}),d(be,R)},"header-right":(be,Fe)=>{var R=Ho(),te=h(R),H=h(te),re=Q=>{const ne=Se(()=>(_(o()),f(()=>!o().disabled)));fr(Q,{size:1,get checked(){return n(ne)},$$events:{change:()=>{o()&&A()(o().id),n(y)()}}})};K(H,Q=>{_(Or),f(Or)&&Q(re)});var de=p(H,2);qe.Root(de,{get requestClose(){return n(y)},set requestClose(Q){g(y,Q)},children:(Q,ne)=>{var ge=qo(),ce=$e(ge);qe.Trigger(ce,{children:(ue,q)=>{Qt(ue,{size:1,variant:"ghost-block",color:"neutral",children:(pe,J)=>{on(pe,{})},$$slots:{default:!0}})},$$slots:{default:!0}});var ee=p(ce,2);qe.Content(ee,{side:"bottom",align:"end",children:(ue,q)=>{var pe=Vo(),J=$e(pe);qe.Item(J,{onSelect:k,children:(he,xe)=>{var Re=jo(),Ee=h(Re);ln(Ee,{});var Ke=p(Ee,2);ve(Ke,{size:1,weight:"medium",children:(Ge,pt)=>{var ot=Z("Edit");d(Ge,ot)},$$slots:{default:!0}}),d(he,Re)},$$slots:{default:!0}});var oe=p(J,2);qe.Item(oe,{onSelect:()=>{(function(){if(o()){const he=Ds.convertServerToJSON(o());navigator.clipboard.writeText(he)}})(),n(y)()},children:(he,xe)=>{var Re=Do(),Ee=h(Re);en(Ee,{});var Ke=p(Ee,2);ve(Ke,{size:1,weight:"medium",children:(Ge,pt)=>{var ot=Z("Copy JSON");d(Ge,ot)},$$slots:{default:!0}}),d(he,Re)},$$slots:{default:!0}});var G=p(oe,2);qe.Item(G,{color:"error",onSelect:()=>{c()(o().id),n(y)()},children:(he,xe)=>{var Re=Uo(),Ee=h(Re);ea(Ee,{});var Ke=p(Ee,2);ve(Ke,{size:1,weight:"medium",children:(Ge,pt)=>{var ot=Z("Delete");d(Ge,ot)},$$slots:{default:!0}}),d(he,Re)},$$slots:{default:!0}}),d(ue,pe)},$$slots:{default:!0}}),d(Q,ge)},$$slots:{default:!0},$$legacy:!0}),d(be,R)}}})},footer:(F,Ce)=>{var be=Jo();at(be,5,()=>n(t),Dt,(Fe,R)=>{var te=Bo(),H=h(te),re=h(H),de=h(re);let Q;var ne=p(de,2);ve(ne,{size:1,weight:"medium",children:(ee,ue)=>{var q=Z();we(()=>Ie(q,(n(R),f(()=>n(R).definition.mcp_tool_name||n(R).definition.name)))),d(ee,q)},$$slots:{default:!0}});var ge=p(re,2),ce=h(ge);Lt(ce,{get content(){return n(R),f(()=>n(R).definition.description)},align:"start",children:(ee,ue)=>{var q=tt(),pe=$e(q),J=oe=>{ve(oe,{size:1,color:"secondary",children:(G,he)=>{var xe=Z();we(()=>Ie(xe,(n(R),f(()=>n(R).definition.description)))),d(G,xe)},$$slots:{default:!0}})};K(pe,oe=>{n(R),f(()=>n(R).definition.description)&&oe(J)}),d(ee,q)},$$slots:{default:!0}}),we(ee=>Q=_t(de,1,"tool-status-dot svelte-igdbzh",null,Q,ee),[()=>({enabled:n(R).enabled,disabled:!n(R).enabled})],Se),d(Fe,te)}),d(F,be)}},$$legacy:!0})},Oe=me=>{var F=Qo(),Ce=h(F),be=h(Ce),Fe=h(be),R=h(Fe),te=h(R);ja(te);var H=p(R,2);ve(H,{color:"secondary",size:1,weight:"medium",children:(G,he)=>{var xe=Z();we(()=>Ie(xe,n(i))),d(G,xe)},$$slots:{default:!0}});var re=p(be,2),de=G=>{var he=Go(),xe=$e(he),Re=h(xe),Ee=h(Re);ve(Ee,{size:1,weight:"medium",children:(ot,rs)=>{var vt=Z("Code Snippet");d(ot,vt)},$$slots:{default:!0}});var Ke=p(xe,2),Ge=h(Ke),pt=h(Ge);oa(pt,{size:1,placeholder:"Paste JSON here...",get value(){return n(N)},set value(ot){g(N,ot)},$$legacy:!0}),d(G,he)},Q=(G,he)=>{var xe=Re=>{var Ee=Xo(),Ke=$e(Ee),Ge=st=>{var Ue=Wo(),Qe=h(Ue),wt=h(Qe);ve(wt,{size:1,weight:"medium",children:(It,Us)=>{var ys=Z("Connection Type");d(It,ys)},$$slots:{default:!0}});var rt=p(wt,2),Et=h(rt);const Gt=Se(()=>n(S)==="http"?"solid":"ghost"),fs=Se(()=>n(S)==="http"?"accent":"neutral");He(Et,{size:1,get variant(){return n(Gt)},get color(){return n(fs)},type:"button",$$events:{click:()=>g(S,"http")},children:(It,Us)=>{var ys=Z("HTTP");d(It,ys)},$$slots:{default:!0}});var Le=p(Et,2);const Ye=Se(()=>n(S)==="sse"?"solid":"ghost"),mt=Se(()=>n(S)==="sse"?"accent":"neutral");He(Le,{size:1,get variant(){return n(Ye)},get color(){return n(mt)},type:"button",$$events:{click:()=>g(S,"sse")},children:(It,Us)=>{var ys=Z("SSE");d(It,ys)},$$slots:{default:!0}}),d(st,Ue)};K(Ke,st=>{_(m()),_(o()),f(()=>{var Ue,Qe;return m()==="addRemote"||m()==="edit"&&(((Ue=o())==null?void 0:Ue.type)==="http"||((Qe=o())==null?void 0:Qe.type)==="sse")})&&st(Ge)});var pt=p(Ke,2),ot=h(pt),rs=h(ot);Yt(rs,{size:1,placeholder:"Enter a name for your MCP server (e.g., 'Server Memory')",get value(){return n(j)},set value(st){g(j,st)},$$events:{focus:k},$$slots:{label:(st,Ue)=>{ve(st,{slot:"label",size:1,weight:"medium",children:(Qe,wt)=>{var rt=Z("Name");d(Qe,rt)},$$slots:{default:!0}})}},$$legacy:!0});var vt=p(pt,2),Jt=st=>{var Ue=Ko(),Qe=h(Ue),wt=h(Qe);Yt(wt,{size:1,placeholder:"Enter the URL (e.g., 'https://api.example.com/mcp')",get value(){return n(I)},set value(rt){g(I,rt)},$$events:{focus:k},$$slots:{label:(rt,Et)=>{ve(rt,{slot:"label",size:1,weight:"medium",children:(Gt,fs)=>{var Le=Z("URL");d(Gt,Le)},$$slots:{default:!0}})}},$$legacy:!0}),d(st,Ue)},as=st=>{var Ue=Yo(),Qe=h(Ue),wt=h(Qe);Yt(wt,{size:1,placeholder:"Enter the MCP command (e.g., 'npx -y @modelcontextprotocol/server-memory')",get value(){return n(b)},set value(rt){g(b,rt)},$$events:{focus:k},$$slots:{label:(rt,Et)=>{ve(rt,{slot:"label",size:1,weight:"medium",children:(Gt,fs)=>{var Le=Z("Command");d(Gt,Le)},$$slots:{default:!0}})}},$$legacy:!0}),d(st,Ue)};K(vt,st=>{_(m()),_(o()),f(()=>{var Ue,Qe;return m()==="addRemote"||((Ue=o())==null?void 0:Ue.type)==="http"||((Qe=o())==null?void 0:Qe.type)==="sse"})?st(Jt):st(as,!1)}),d(Re,Ee)};K(G,Re=>{m()!=="add"&&m()!=="addRemote"&&m()!=="edit"||Re(xe)},he)};K(re,G=>{m()==="addJson"?G(de):G(Q,!1)});var ne=p(re,2),ge=G=>{Oo(G,{handleEnterEditMode:k,get envVarEntries(){return n(E)},set envVarEntries(he){g(E,he)},$$legacy:!0})};K(ne,G=>{_(m()),_(bt),_(o()),f(()=>(m()==="add"||m()==="edit")&&!bt(o()))&&G(ge)});var ce=p(ne,2),ee=h(ce);let ue;var q=h(ee);Gs(q,{variant:"soft",color:"error",size:1,children:(G,he)=>{var xe=Z();we(()=>Ie(xe,U())),d(G,xe)},$$slots:{default:!0,icon:(G,he)=>{Da(G,{slot:"icon"})}}});var pe=p(ee,2),J=h(pe);He(J,{size:1,variant:"ghost",color:"neutral",type:"button",$$events:{click:se},children:(G,he)=>{var xe=Z("Cancel");d(G,xe)},$$slots:{default:!0}});var oe=p(J,2);He(oe,{size:1,variant:"solid",color:"accent",get loading(){return ae()},type:"submit",get disabled(){return n(l)},children:(G,he)=>{var xe=tt(),Re=$e(xe),Ee=Ge=>{var pt=Z("Import");d(Ge,pt)},Ke=(Ge,pt)=>{var ot=vt=>{var Jt=Z("Add");d(vt,Jt)},rs=(vt,Jt)=>{var as=Ue=>{var Qe=Z("Add");d(Ue,Qe)},st=(Ue,Qe)=>{var wt=rt=>{var Et=Z("Save");d(rt,Et)};K(Ue,rt=>{m()==="edit"&&rt(wt)},Qe)};K(vt,Ue=>{m()==="addRemote"?Ue(as):Ue(st,!1)},Jt)};K(Ge,vt=>{m()==="add"?vt(ot):vt(rs,!1)},pt)};K(Re,Ge=>{m()==="addJson"?Ge(Ee):Ge(Ke,!1)}),d(G,xe)},$$slots:{default:!0}}),we(G=>{_t(F,1,"c-mcp-server-card "+(m()==="add"||m()==="addJson"||m()==="addRemote"?"add-server-section":"server-item"),"svelte-igdbzh"),ue=_t(ee,1,"error-container svelte-igdbzh",null,ue,G)},[()=>({"is-error":!!U()})],Se),ut("submit",F,nn(M)),d(me,F)};return K(T,me=>{m()==="view"&&o()?me(le):me(Oe,!1)}),d(r,Y),ia(e,"setLocalEnvVarFormState",O),Je({setLocalEnvVarFormState:O})}var el=v('<div class="user-input-field svelte-8tbe79"><!> <!> <!></div>'),tl=v('<div class="user-input-container svelte-8tbe79"><!> <div class="user-input-actions svelte-8tbe79"><!> <!></div></div>'),sl=v('<div slot="header-left" class="mcp-service-info svelte-8tbe79"><div class="mcp-service-title svelte-8tbe79"><!></div> <!> <!></div>'),rl=v('<div class="installed-indicator svelte-8tbe79"><!></div>'),al=v('<div slot="header-right" class="mcp-service-actions svelte-8tbe79"><!></div>'),nl=v('<div class="mcp-service-item"><!></div>'),il=v('<div class="mcp-install-content svelte-8tbe79"><div class="mcp-list-container svelte-8tbe79"></div></div>'),ol=v('<div slot="header-left" class="mcp-install-left svelte-8tbe79"><!> <!></div>'),ll=v('<div slot="header" class="mcp-install-header svelte-8tbe79"><!></div>'),dl=v('<div class="mcp-install-wrapper svelte-8tbe79"><!></div>');function cl(r,e){Be(e,!1);let t=C(e,"onMCPServerAdd",24,()=>{}),s=C(e,"servers",24,()=>[]);const a=[{label:dr.REDIS,description:"Real-time data platform for building fast apps",command:"uvx",args:["--from","git+https://github.com/redis/mcp-redis.git","redis-mcp-server","--url"],userInput:[{label:"Redis connection URL",description:"Enter your connection URL (redis://localhost:6379/0)",placeholder:"rediss://<USERNAME>:<PASSWORD>@<HOST>:<PORT>?ssl_cert_reqs=required&ssl_ca_certs=<PATH_TO_CERT>",correspondingArg:"--url",type:"argument"}]},{label:dr.MONGODB,description:"Optimize database queries and performance.",command:"npx",args:["-y","mongodb-mcp-server","--connectionString"],userInput:[{label:"MongoDB Connection String",description:"Enter your MongoDB connection string",placeholder:"********************************:port/database",correspondingArg:"--connectionString",type:"argument"}]},{label:dr.CIRCLECI,description:"Debug builds and improve CI/CD pipelines.",command:"npx",args:["-y","@circleci/mcp-server-circleci"],userInput:[{label:"CircleCI Token",description:"Enter your CircleCI token",placeholder:"YOUR_CIRCLE_CI_TOKEN",type:"environmentVariable",envVarName:"CIRCLECI_TOKEN"},{label:"Base URL",description:"Enter the base URL for your CircleCI instance",placeholder:"https://circleci.com",defaultValue:"https://circleci.com",type:"environmentVariable",envVarName:"CIRCLECI_BASE_URL"}]},{label:"Context 7",description:"Package documentation",command:"npx -y @upstash/context7-mcp@latest"},{label:"Playwright",description:"Browser automation",command:"npx -y @playwright/mcp@latest"},{label:"Sequential thinking",description:"Think through complex problems step-by-step.",command:"npx -y @modelcontextprotocol/server-sequential-thinking"}],l="easyMCPInstall.collapsed";let i=X(!1),o=X(!1),c=X(null),u=X({}),w=X({});function D(x){var j;if(!x.userInput)return;for(let b=0;b<x.userInput.length;b++){const I=x.userInput[b];let z;if(z=I.type==="environmentVariable"&&I.envVarName?I.envVarName:I.correspondingArg?I.correspondingArg:`input_${b}`,!((j=n(u)[z])==null?void 0:j.trim())){const S=n(w)[z];return void(S&&S.focus())}}let P=[x.command],m={};x.args&&P.push(...x.args);for(let b=0;b<x.userInput.length;b++){const I=x.userInput[b];let z;z=I.type==="environmentVariable"&&I.envVarName?I.envVarName:I.correspondingArg?I.correspondingArg:`input_${b}`;const N=n(u)[z].trim(),S=`"${N}"`;if(I.type==="environmentVariable"&&I.envVarName)m[I.envVarName]=N;else if(I.correspondingArg){const E=P.indexOf(I.correspondingArg);E!==-1?P.splice(E+1,0,S):P.push(I.correspondingArg,S)}else P.push(S)}const U={type:"stdio",name:x.label,command:P.join(" "),arguments:"",useShellInterpolation:!0,env:Object.keys(m).length>0?m:void 0};t()&&t()(U),g(c,null),g(u,{})}function A(){g(c,null),g(u,{})}Me(()=>{},()=>{const x=localStorage.getItem(l);if(x!==null)try{g(i,JSON.parse(x))}catch{localStorage.removeItem(l)}g(o,!0)}),Me(()=>(n(o),n(i)),()=>{typeof window<"u"&&n(o)&&localStorage.setItem(l,JSON.stringify(n(i)))}),it(),We();var ke=dl(),ye=h(ke);na(ye,{get collapsed(){return n(i)},set collapsed(x){g(i,x)},children:(x,P)=>{var m=il(),U=h(m);at(U,5,()=>a,Dt,(j,b)=>{var I=nl();Tt(h(I),{$$slots:{"header-left":(z,N)=>{var S=sl(),E=h(S),B=h(E);ve(B,{size:1,weight:"medium",children:(L,M)=>{var se=Z();we(()=>Ie(se,(n(b),f(()=>n(b).label)))),d(L,se)},$$slots:{default:!0}});var O=p(E,2),y=L=>{ve(L,{size:1,color:"secondary",children:(M,se)=>{var Y=Z();we(()=>Ie(Y,(n(b),f(()=>n(b).description)))),d(M,Y)},$$slots:{default:!0}})};K(O,L=>{n(b),f(()=>n(b).description)&&L(y)});var k=p(O,2),ae=L=>{var M=tl(),se=h(M);at(se,1,()=>(n(b),f(()=>n(b).userInput)),Dt,(Oe,Ne,me)=>{var F=el();const Ce=Se(()=>(n(Ne),f(()=>n(Ne).type==="environmentVariable"&&n(Ne).envVarName?n(Ne).envVarName:n(Ne).correspondingArg||`input_${me}`)));var be=h(F);ve(be,{size:1,weight:"medium",color:"neutral",children:(re,de)=>{var Q=Z();we(()=>Ie(Q,(n(Ne),f(()=>n(Ne).label)))),d(re,Q)},$$slots:{default:!0}});var Fe=p(be,2),R=re=>{ve(re,{size:1,color:"secondary",children:(de,Q)=>{var ne=Z();we(()=>Ie(ne,(n(Ne),f(()=>n(Ne).description)))),d(de,ne)},$$slots:{default:!0}})};K(Fe,re=>{n(Ne),f(()=>n(Ne).description)&&re(R)});var te=p(Fe,2);const H=Se(()=>(n(Ne),f(()=>n(Ne).placeholder||"")));Yt(te,{get placeholder(){return n(H)},size:1,variant:"surface",get value(){return n(u)[n(Ce)]},set value(re){or(u,n(u)[n(Ce)]=re)},get textInput(){return n(w)[n(Ce)]},set textInput(re){or(w,n(w)[n(Ce)]=re)},$$events:{keydown:re=>{re.key==="Enter"?D(n(b)):re.key==="Escape"&&A()}},$$legacy:!0}),d(Oe,F)});var Y=p(se,2),T=h(Y);He(T,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>D(n(b))},children:(Oe,Ne)=>{var me=Z("Install");d(Oe,me)},$$slots:{default:!0}});var le=p(T,2);He(le,{variant:"ghost-block",color:"neutral",size:1,$$events:{click:A},children:(Oe,Ne)=>{var me=Z("Cancel");d(Oe,me)},$$slots:{default:!0}}),d(L,M)};K(k,L=>{n(c),n(b),f(()=>n(c)===n(b).label&&n(b).userInput)&&L(ae)}),d(z,S)},"header-right":(z,N)=>{var S=al(),E=h(S),B=y=>{var k=rl(),ae=h(k);Mr.Root(ae,{color:"success",size:1,variant:"soft",children:(L,M)=>{var se=Z("Installed");d(L,se)},$$slots:{default:!0}}),d(y,k)},O=(y,k)=>{var ae=L=>{Qt(L,{variant:"ghost-block",color:"accent",size:1,$$events:{click:()=>function(M){if(s().some(Y=>Y.name===M.label))return;if(M.userInput&&M.userInput.length>0)return g(u,{}),M.userInput.forEach((Y,T)=>{let le;le=Y.type==="environmentVariable"&&Y.envVarName?Y.envVarName:Y.correspondingArg?Y.correspondingArg:`input_${T}`,or(u,n(u)[le]=Y.defaultValue||"")}),void g(c,M.label);const se={type:"stdio",name:M.label,command:M.command,arguments:"",useShellInterpolation:!0};t()&&t()(se)}(n(b))},children:(M,se)=>{hs(M,{})},$$slots:{default:!0}})};K(y,L=>{n(c),n(b),f(()=>n(c)!==n(b).label)&&L(ae)},k)};K(E,y=>{_(s()),n(b),f(()=>s().some(k=>k.name===n(b).label))?y(B):y(O,!1)}),d(z,S)}}}),d(j,I)}),d(x,m)},$$slots:{default:!0,header:(x,P)=>{var m=ll();Tt(h(m),{$$slots:{"header-left":(U,j)=>{var b=ol(),I=h(b);rn(I,{});var z=p(I,2);ve(z,{color:"neutral",size:1,weight:"light",class:"card-title",children:(N,S)=>{var E=Z("Easy MCP Installation");d(N,E)},$$slots:{default:!0}}),d(U,b)}}}),d(x,m)}},$$legacy:!0}),d(r,ke),Je()}const ul={mcpDocsURL:"https://docs.augmentcode.com/setup-augment/mcp"},hl={mcpDocsURL:"https://docs.augmentcode.com/jetbrains/setup-augment/mcp"},pl=Ra(),vl=new class{constructor(r){ze(this,"strings");let e={[lr.vscode]:{},[lr.jetbrains]:hl,[lr.web]:{}};this.strings={...ul,...e[r]}}get(r){return this.strings[r]}}(pl.clientType);var ml=v('<div class="section-heading-text">MCP</div>'),gl=v(`<div class="mcp-servers svelte-1vnq4q3"><div class="section-heading svelte-1vnq4q3"><!></div> <div class="description-text svelte-1vnq4q3">Configure a new Model Context Protocol server to connect Augment to custom tools. Find out more
    about MCP <a>in the docs</a>.</div> <!> <!></div> <!> <div class="add-mcp-button-container svelte-1vnq4q3"><!> <!> <!></div>`,1),fl=v('<div class="section-heading-text">Terminal</div>'),yl=v("<!> <!>",1),_l=v('<div class="terminal-settings svelte-dndd5n"><!> <div class="shell-selector svelte-dndd5n"><!> <!></div> <div class="startup-script-container svelte-dndd5n"><!> <!></div></div>');function Cl(r,e){Be(e,!1);const t=X();let s=C(e,"supportedShells",24,()=>[]),a=C(e,"selectedShell",24,()=>{}),l=C(e,"startupScript",28,()=>{}),i=C(e,"onShellSelect",8),o=C(e,"onStartupScriptChange",8),c=X();Me(()=>_(a()),()=>{var m;g(t,a()?(m=a(),s().find(U=>U.friendlyName===m)):void 0)}),it(),We();var u=_l(),w=h(u);ve(w,{size:1,weight:"regular",color:"secondary",children:(m,U)=>{var j=fl();d(m,j)},$$slots:{default:!0}});var D=p(w,2),A=h(D);ve(A,{size:1,children:(m,U)=>{var j=Z("Shell:");d(m,j)},$$slots:{default:!0}});var ke=p(A,2);qe.Root(ke,{get requestClose(){return n(c)},set requestClose(m){g(c,m)},children:(m,U)=>{var j=yl(),b=$e(j);qe.Trigger(b,{children:(z,N)=>{const S=Se(()=>(_(s()),f(()=>s().length===0)));He(z,{size:1,variant:"outline",color:"neutral",get disabled(){return n(S)},children:(E,B)=>{var O=tt(),y=$e(O),k=L=>{var M=Z();we(()=>Ie(M,`${n(t),f(()=>n(t).friendlyName)??""}
            (${n(t),f(()=>n(t).supportString)??""})`)),d(L,M)},ae=(L,M)=>{var se=T=>{var le=Z("No shells available");d(T,le)},Y=T=>{var le=Z("Select a shell");d(T,le)};K(L,T=>{_(s()),f(()=>s().length===0)?T(se):T(Y,!1)},M)};K(y,L=>{n(t),_(s()),f(()=>n(t)&&s().length>0)?L(k):L(ae,!1)}),d(E,O)},$$slots:{default:!0,iconRight:(E,B)=>{Va(E)}}})},$$slots:{default:!0}});var I=p(b,2);qe.Content(I,{side:"bottom",align:"start",children:(z,N)=>{var S=tt(),E=$e(S),B=y=>{var k=tt(),ae=$e(k);at(ae,1,s,L=>L.friendlyName,(L,M)=>{const se=Se(()=>(_(a()),n(M),f(()=>a()===n(M).friendlyName)));qe.Item(L,{onSelect:()=>{i()(n(M).friendlyName),n(c)()},get highlight(){return n(se)},children:(Y,T)=>{var le=Z();we(()=>Ie(le,`${n(M),f(()=>n(M).friendlyName)??""}
              (${n(M),f(()=>n(M).supportString)??""})`)),d(Y,le)},$$slots:{default:!0}})}),d(y,k)},O=y=>{qe.Label(y,{children:(k,ae)=>{var L=Z("No shells available");d(k,L)},$$slots:{default:!0}})};K(E,y=>{_(s()),f(()=>s().length>0)?y(B):y(O,!1)}),d(z,S)},$$slots:{default:!0}}),d(m,j)},$$slots:{default:!0},$$legacy:!0});var ye=p(D,2),x=h(ye);ve(x,{size:1,children:(m,U)=>{var j=Z("Start-up script: Code to run wherever a new terminal is opened");d(m,j)},$$slots:{default:!0}});var P=p(x,2);oa(P,{placeholder:"Enter shell commands to run on terminal startup",resize:"vertical",get value(){return l()},set value(m){l(m)},$$events:{change:function(m){const U=m.target;o()(U.value)}},$$legacy:!0}),d(r,u),Je()}var wl=v('<div class="section-heading-text">Sound Settings</div>'),bl=v('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),$l=v('<div slot="header-right"><!></div>'),Sl=v('<div class="c-sound-setting__info svelte-8awonv" slot="header-left"><div><!></div> <div><!></div></div>'),kl=v('<div slot="header-right"><!></div>'),xl=v('<!> <div class="c-sound-settings svelte-8awonv"><!> <!></div>',1),Ml=v('<div class="section-heading-text">Agent Settings</div>'),Al=v('<div class="c-agent-setting__info svelte-mv39d5" slot="header-left"><div><!></div> <div><!></div> <div class="c-agent-setting__education svelte-mv39d5"><!></div></div>'),Tl=v('<div slot="header-right"><!></div>'),Nl=v('<!> <div class="c-agent-settings svelte-mv39d5"><!></div>',1),Rl=v('<div class="c-settings-tools svelte-181yusq"><!> <!> <!> <!> <!></div>');function El(r,e){let t=C(e,"tools",24,()=>[]),s=C(e,"isMCPEnabled",8,!0),a=C(e,"isMCPImportEnabled",8,!0),l=C(e,"isTerminalEnabled",8,!0),i=C(e,"isSoundCategoryEnabled",8,!1),o=C(e,"isAgentCategoryEnabled",8,!1),c=C(e,"isSwarmModeFeatureFlagEnabled",8,!1),u=C(e,"hasEverUsedRemoteAgent",8,!1),w=C(e,"onAuthenticate",8),D=C(e,"onRevokeAccess",8),A=C(e,"onToolApprovalConfigChange",8,()=>{}),ke=C(e,"onMCPServerAdd",8),ye=C(e,"onMCPServerSave",8),x=C(e,"onMCPServerDelete",8),P=C(e,"onMCPServerToggleDisable",8),m=C(e,"onMCPServerJSONImport",8),U=C(e,"onCancel",24,()=>{}),j=C(e,"supportedShells",24,()=>[]),b=C(e,"selectedShell",24,()=>{}),I=C(e,"startupScript",24,()=>{}),z=C(e,"onShellSelect",8,()=>{}),N=C(e,"onStartupScriptChange",8,()=>{});var S=Rl(),E=h(S);Eo(E,{title:"Services",get tools(){return t()},get onAuthenticate(){return w()},get onRevokeAccess(){return D()},onToolApprovalConfigChange:A()});var B=p(E,2),O=Y=>{(function(T,le){Be(le,!1);const[Oe,Ne]=Mt(),me=()=>Xe(pe,"$allServers",Oe),F=X(),Ce=X();let be=C(le,"onMCPServerAdd",8),Fe=C(le,"onMCPServerSave",8),R=C(le,"onMCPServerDelete",8),te=C(le,"onMCPServerToggleDisable",8),H=C(le,"onCancel",24,()=>{}),re=C(le,"onMCPServerJSONImport",8),de=C(le,"isMCPImportEnabled",8,!0),Q=X(null),ne=X(null);function ge(){var Le;g(Q,null),g(ne,null),(Le=H())==null||Le()}let ce=X([]);const ee=ds(ks.key),ue=Nr(),q=ee.getEnableNativeRemoteMcp(),pe=ue.getServers();function J(Le){g(Q,Le.id)}function oe(Le){return async function(...Ye){const mt=await Le(...Ye);return g(ne,null),g(Q,null),mt}}const G=oe(be()),he=oe(Fe()),xe=oe(re()),Re=oe(R()),Ee=oe(te()),Ke=vl.get("mcpDocsURL");Me(()=>(n(ne),n(Q)),()=>{g(F,n(ne)==="add"||n(ne)==="addJson"||n(ne)==="addRemote"||n(Q)!==null)}),Me(()=>me(),()=>{g(ce,q?Ua(me()):me())}),Me(()=>n(ce),()=>{g(Ce,Ds.parseServerValidationMessages(n(ce)))}),it(),We();var Ge=gl(),pt=$e(Ge),ot=h(pt),rs=h(ot);ve(rs,{size:1,weight:"regular",color:"secondary",children:(Le,Ye)=>{var mt=ml();d(Le,mt)},$$slots:{default:!0}});var vt=p(ot,2),Jt=p(h(vt)),as=p(vt,2);cl(as,{get onMCPServerAdd(){return G},get servers(){return n(ce)}});var st=p(as,2);at(st,1,()=>n(ce),Le=>Le.id,(Le,Ye)=>{const mt=Se(()=>(n(Q),n(Ye),f(()=>n(Q)===n(Ye).id?"edit":"view"))),It=Se(()=>(n(Ce),n(Ye),f(()=>n(Ce).errors.get(n(Ye).id)))),Us=Se(()=>(n(Ce),n(Ye),f(()=>n(Ce).warnings.get(n(Ye).id))));Wr(Le,{get mode(){return n(mt)},get server(){return n(Ye)},get onAdd(){return G},get onSave(){return he},get onDelete(){return Re},get onToggleDisableServer(){return Ee},onEdit:J,onCancel:ge,get onJSONImport(){return xe},get disabledText(){return n(It)},get warningText(){return n(Us)}})});var Ue=p(pt,2),Qe=Le=>{Wr(Le,{get mode(){return n(ne)},get onAdd(){return G},get onSave(){return he},get onDelete(){return Re},get onToggleDisableServer(){return Ee},onEdit:J,onCancel:ge,get onJSONImport(){return xe}})};K(Ue,Le=>{n(ne)!=="add"&&n(ne)!=="addJson"&&n(ne)!=="addRemote"||Le(Qe)});var wt=p(Ue,2),rt=h(wt);He(rt,{get disabled(){return n(F)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{g(ne,"add")}},children:(Le,Ye)=>{var mt=Z("Add MCP");d(Le,mt)},$$slots:{default:!0,iconLeft:(Le,Ye)=>{hs(Le,{slot:"iconLeft"})}}});var Et=p(rt,2);He(Et,{get disabled(){return n(F)},color:"neutral",variant:"soft",size:1,$$events:{click:()=>{g(ne,"addRemote")}},children:(Le,Ye)=>{var mt=Z("Add remote MCP");d(Le,mt)},$$slots:{default:!0,iconLeft:(Le,Ye)=>{hs(Le,{slot:"iconLeft"})}}});var Gt=p(Et,2),fs=Le=>{He(Le,{get disabled(){return n(F)},color:"neutral",variant:"soft",size:1,title:"Add MCP from JSON",$$events:{click:()=>{g(ne,"addJson")}},children:(Ye,mt)=>{var It=Z("Import from JSON");d(Ye,It)},$$slots:{default:!0,iconLeft:(Ye,mt)=>{ra(Ye,{slot:"iconLeft"})}}})};K(Gt,Le=>{de()&&Le(fs)}),we(()=>Kt(Jt,"href",Ke)),d(T,Ge),Je(),Ne()})(Y,{get onMCPServerAdd(){return ke()},get onMCPServerSave(){return ye()},get onMCPServerDelete(){return x()},get onMCPServerToggleDisable(){return P()},get onMCPServerJSONImport(){return m()},get onCancel(){return U()},get isMCPImportEnabled(){return a()}})};K(B,Y=>{s()&&Y(O)});var y=p(B,2),k=Y=>{Cl(Y,{get supportedShells(){return j()},get selectedShell(){return b()},get startupScript(){return I()},onShellSelect:z(),onStartupScriptChange:N()})};K(y,Y=>{l()&&Y(k)});var ae=p(y,2),L=Y=>{(function(T,le){Be(le,!1);const[Oe,Ne]=Mt(),me=()=>Xe(n(F),"$currentSettings",Oe),F=X(),Ce=X(),be=ds(yr.key);async function Fe(){return await be.playAgentComplete(),"success"}Me(()=>{},()=>{Js(g(F,be.getCurrentSettings),"$currentSettings",Oe)}),Me(()=>me(),()=>{g(Ce,me().enabled)}),it(),We();var R=xl(),te=$e(R);ve(te,{size:1,weight:"regular",color:"secondary",children:(ne,ge)=>{var ce=wl();d(ne,ce)},$$slots:{default:!0}});var H=p(te,2),re=h(H);Tt(re,{$$slots:{"header-left":(ne,ge)=>{var ce=bl(),ee=h(ce),ue=h(ee);ve(ue,{size:2,weight:"medium",children:(J,oe)=>{var G=Z("Enable Sound Effects");d(J,G)},$$slots:{default:!0}});var q=p(ee,2),pe=h(q);ve(pe,{size:1,weight:"medium",children:(J,oe)=>{var G=Z("Play a sound when an agent completes a task");d(J,G)},$$slots:{default:!0}}),d(ne,ce)},"header-right":(ne,ge)=>{var ce=$l(),ee=h(ce);fr(ee,{size:1,get checked(){return n(Ce)},$$events:{change:()=>be.updateEnabled(!n(Ce))}}),d(ne,ce)}}});var de=p(re,2),Q=ne=>{Tt(ne,{$$slots:{"header-left":(ge,ce)=>{var ee=Sl(),ue=h(ee),q=h(ue);ve(q,{size:2,weight:"medium",children:(oe,G)=>{var he=Z("Test Sound");d(oe,he)},$$slots:{default:!0}});var pe=p(ue,2),J=h(pe);ve(J,{size:1,weight:"medium",children:(oe,G)=>{var he=Z("Play a sample of the agent completion sound");d(oe,he)},$$slots:{default:!0}}),d(ge,ee)},"header-right":(ge,ce)=>{var ee=kl(),ue=h(ee);const q=Se(()=>n(Ce)?"":"Enable sound effects to test"),pe=Se(()=>(_(ps),f(()=>[ps.Hover])));Lt(ue,{get content(){return n(q)},get triggerOn(){return n(pe)},children:(J,oe)=>{const G=Se(()=>!n(Ce));dn(J,{size:1,defaultColor:"neutral",get enabled(){return n(Ce)},stickyColor:!1,get disabled(){return n(G)},onClick:Fe,tooltip:{neutral:"Play a sample of the agent completion sound",success:"Played!"},children:(he,xe)=>{var Re=Z("Play");d(he,Re)},$$slots:{default:!0}})},$$slots:{default:!0}}),d(ge,ee)}}})};K(de,ne=>{n(Ce)&&ne(Q)}),d(T,R),Je(),Ne()})(Y,{})};K(ae,Y=>{i()&&Y(L)});var M=p(ae,2),se=Y=>{(function(T,le){Be(le,!1);const[Oe,Ne]=Mt(),me=()=>Xe(n(F),"$currentSettings",Oe),F=X(),Ce=X();let be=C(le,"isSwarmModeEnabled",8,!1),Fe=C(le,"hasEverUsedRemoteAgent",8,!1);const R=ds(Ps.key);Me(()=>{},()=>{Js(g(F,R.getCurrentSettings),"$currentSettings",Oe)}),Me(()=>me(),()=>{g(Ce,me().enabled)}),it(),We();var te=tt(),H=$e(te),re=de=>{var Q=Nl(),ne=$e(Q);ve(ne,{size:1,weight:"regular",color:"secondary",children:(ce,ee)=>{var ue=Ml();d(ce,ue)},$$slots:{default:!0}});var ge=p(ne,2);Tt(h(ge),{$$slots:{"header-left":(ce,ee)=>{var ue=Al(),q=h(ue),pe=h(q);ve(pe,{size:2,weight:"medium",children:(xe,Re)=>{var Ee=Z("Enable Swarm Mode");d(xe,Ee)},$$slots:{default:!0}});var J=p(q,2),oe=h(J);ve(oe,{size:1,weight:"medium",children:(xe,Re)=>{var Ee=Z("Allow agents to coordinate and work together on complex tasks");d(xe,Ee)},$$slots:{default:!0}});var G=p(J,2),he=h(G);ve(he,{size:1,weight:"regular",color:"secondary",children:(xe,Re)=>{var Ee=Z(`Sub-agents run in isolated remote environments and communicate via git repositories.
            Each remote agent consumes credits from your account. Changes are reviewed before being
            applied to your local workspace.`);d(xe,Ee)},$$slots:{default:!0}}),d(ce,ue)},"header-right":(ce,ee)=>{var ue=Tl(),q=h(ue);fr(q,{size:1,get checked(){return n(Ce)},$$events:{change:()=>R.updateEnabled(!n(Ce))}}),d(ce,ue)}}}),d(de,Q)};K(H,de=>{be()&&Fe()&&de(re)}),d(T,te),Je(),Ne()})(Y,{get isSwarmModeEnabled(){return c()},get hasEverUsedRemoteAgent(){return u()}})};K(M,Y=>{o()&&Y(se)}),d(r,S)}var Il=Ct("<svg><!></svg>");function zl(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=Il();Fs(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 576 512",...t}));var a=h(s);ar(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M288 0c-8.5 0-17 1.7-24.8 5.1L53.9 94.8C40.6 100.5 32 113.5 32 128s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2L312.8 5.1C305 1.7 296.5 0 288 0m-5.9 49.2c1.9-.8 3.9-1.2 5.9-1.2s4 .4 5.9 1.2L477.7 128l-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 128zM53.9 222.8C40.6 228.5 32 241.5 32 256s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 256l47.7-20.5-60.9-26.1zm0 128C40.6 356.5 32 369.5 32 384s8.6 27.5 21.9 33.2l209.3 89.7c7.8 3.4 16.3 5.1 24.8 5.1s17-1.7 24.8-5.1l209.3-89.7c13.3-5.7 21.9-18.8 21.9-33.2s-8.6-27.5-21.9-33.2l-31.2-13.4-60.9 26.1 47.7 20.5-183.8 78.8c-1.9.8-3.9 1.2-5.9 1.2s-4-.4-5.9-1.2L98.3 384l47.7-20.5-60.9-26.1z"/>',!0),d(r,s)}var Ol=Ct("<svg><!></svg>");function Ll(r,e){const t=us(e,["children","$$slots","$$events","$$legacy"]);var s=Ol();Fs(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...t}));var a=h(s);ar(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M63.2 379.3c-6.2-6.2-6.2-16.4 0-22.6l39.4-39.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 41.4-41.4 30.1 30.1c6.2 6.2 16.4 6.2 22.6 0s6.2-16.4 0-22.6l-30.1-30.1 39.4-39.4c6.2-6.2 16.4-6.2 22.6 0l69.5 69.5c6.2 6.2 6.2 16.4 0 22.6L155.3 448.8c-6.2 6.2-16.4 6.2-22.6 0zm35.5 103.4c25 25 65.5 25 90.5 0l293.5-293.4c25-25 25-65.5 0-90.5l-69.4-69.5c-25-25-65.5-25-90.5 0L29.3 322.7c-25 25-25 65.5 0 90.5l69.5 69.5z"/>',!0),d(r,s)}var Zl=v('<div class="c-user-guidelines-category__input svelte-10borzo"><!></div>');function _a(r,e){Be(e,!1);const[t,s]=Mt(),a=xr();let l=C(e,"userGuidelines",12,""),i=C(e,"userGuidelinesLengthLimit",24,()=>{}),o=C(e,"updateUserGuideline",8,()=>!1);const c=nt(void 0);function u(){const A=l().trim();if(Xe(c,"$originalValue",t)!==A){if(!o()(A))throw i()&&A.length>i()?`The user guideline must be less than ${i()} character long`:"An error occurred updating the user";zr(c,A)}}Kr(()=>{zr(c,l().trim())}),Yr(()=>{u()}),We();var w=Zl(),D=h(w);un(D,{placeholder:"Add your guidelines for Augment Chat...",resize:"vertical",saveFunction:u,get value(){return l()},set value(A){l(A)},$$events:{focus:A=>{a("focus",A)}},$$legacy:!0}),d(r,w),Je(),s()}class Pl{constructor(e,t,s){ze(this,"_showCreateRuleDialog",nt(!1));ze(this,"_createRuleError",nt(""));ze(this,"_extensionClient");this._host=e,this._msgBroker=t,this._rulesModel=s;const a=new ta;this._extensionClient=new sa(e,t,a)}async createRule(){this._showCreateRuleDialog.set(!0)}async handleCreateRuleWithName(e){if(e&&e.trim()){this._createRuleError.set("");try{const t=await this._rulesModel.createRule(e.trim());t&&t.path&&await this.openRule(t.path),this._extensionClient.reportAgentSessionEvent({eventName:hr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Vs.manuallyCreated,numFiles:1,source:""}}}),this.hideCreateRuleDialog()}catch{const s=`Failed to create rule "${e.trim()}"`;this._createRuleError.set(s)}}else this.hideCreateRuleDialog()}async openRule(e){try{const t=await this._rulesModel.getWorkspaceRoot();e===Fr?this._extensionClient.openFile({repoRoot:t,pathName:Fr}):this._extensionClient.openFile({repoRoot:t,pathName:`${Wa}/${Ka}/${e}`})}catch(t){console.error("Failed to open rule:",t)}}async deleteRule(e){try{await this._extensionClient.openConfirmationModal({title:"Delete Rule",message:"Are you sure you want to delete this rule?",confirmButtonText:"Delete",cancelButtonText:"Cancel"})&&await this._rulesModel.deleteRule(e)}catch(t){console.error("Failed to delete rule:",t)}}async selectFileToImport(){try{const e=await this._msgBroker.send({type:De.triggerImportDialogRequest},1e5);if(e.data.selectedPaths&&e.data.selectedPaths.length>0){const t=await this._rulesModel.processSelectedPaths(e.data.selectedPaths);this._showImportNotification(t),this._reportSelectedImportMetrics(t)}}catch(e){console.error("Failed to import files:",e)}}async getAutoImportOptions(){return await this._rulesModel.getAutoImportOptions()}async processAutoImportSelection(e){const t=await this._rulesModel.processAutoImportSelection(e);return this._showImportNotification(t),this._reportAutoImportMetrics(t),t}_showImportNotification(e){let t;e.importedRulesCount===0?t=e.source?`No new rules imported from ${e.source}`:"No new rules imported":(t=`Successfully imported ${e.importedRulesCount} rule${e.importedRulesCount!==1?"s":""}`,e.duplicatesCount&&e.duplicatesCount>0&&(t+=` and skipped ${e.duplicatesCount} duplicate${e.duplicatesCount!==1?"s":""}`)),this._extensionClient.showNotification({message:t,type:e.importedRulesCount>0?"info":"warning"})}_reportSelectedImportMetrics(e){const t=e.directoryOrFile==="directory"?Vs.selectedDirectory:(e.directoryOrFile,Vs.selectedFile);this._extensionClient.reportAgentSessionEvent({eventName:hr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:t,numFiles:e.importedRulesCount,source:""}}})}_reportAutoImportMetrics(e){this._extensionClient.reportAgentSessionEvent({eventName:hr.rulesImported,conversationId:"",eventData:{rulesImportedData:{type:Vs.auto,numFiles:e.importedRulesCount,source:e.source}}})}getShowCreateRuleDialog(){return this._showCreateRuleDialog}getCreateRuleError(){return this._createRuleError}hideCreateRuleDialog(){this._showCreateRuleDialog.set(!1),this._createRuleError.set("")}}var Fl=v("<!> <!> <!>",1),jl=v('<div slot="footer"><!> <!></div>'),Dl=v('<input type="text" value="No existing rules found" readonly="" class="c-dropdown-input svelte-z1s6x7"/>'),Ul=v('<div class="c-dropdown-trigger svelte-z1s6x7"><input type="text" readonly="" class="c-dropdown-input svelte-z1s6x7"/> <!></div>'),Vl=v("<!> <!>",1),ql=v("<!> <!>",1),Hl=v("<!> <!>",1),Bl=v("<!> <!> <!> <!>",1),Jl=v('<div slot="body" class="c-auto-import-rules-dialog svelte-z1s6x7"><!></div>'),Gl=v('<div slot="footer"><!> <!></div>');function Wl(r,e){Be(e,!1);const[t,s]=Mt(),a=()=>Xe(n(ke),"$focusedIndex",t),l=X(),i=xr();let o=C(e,"show",8,!1),c=C(e,"options",24,()=>[]),u=C(e,"isLoading",8,!1),w=C(e,"errorMessage",8,""),D=C(e,"successMessage",8,""),A=X(n(l)),ke=X(void 0),ye=X(()=>{});function x(){n(A)&&!u()&&i("select",n(A))}function P(){u()||(i("cancel"),g(A,n(l)))}Me(()=>_(c()),()=>{g(l,c().length>0?c()[0]:null)}),Me(()=>(_(o()),n(l)),()=>{o()&&g(A,n(l))}),it(),We(),ut("keydown",rr,function(m){o()&&!u()&&(m.key==="Escape"?(m.preventDefault(),P()):m.key==="Enter"&&n(A)&&(m.preventDefault(),x()))}),la(r,{get show(){return o()},title:"Auto Import Rules",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return u()},get preventEscapeClose(){return u()},$$events:{cancel:P},$$slots:{body:(m,U)=>{var j=Jl(),b=h(j),I=N=>{var S=Dl();d(N,S)},z=N=>{var S=Bl(),E=$e(S);ve(E,{size:2,color:"secondary",children:(M,se)=>{var Y=Z("Select existing rules to auto import to .augment/rules");d(M,Y)},$$slots:{default:!0}});var B=p(E,2);const O=Se(()=>(_(c()),f(()=>c().length===0?[]:void 0)));qe.Root(B,{get triggerOn(){return n(O)},get requestClose(){return n(ye)},set requestClose(M){g(ye,M)},get focusedIndex(){return n(ke)},set focusedIndex(M){Js(g(ke,M),"$focusedIndex",t)},children:(M,se)=>{var Y=Hl(),T=$e(Y);qe.Trigger(T,{children:(Oe,Ne)=>{var me=Ul(),F=h(me),Ce=p(F,2);Ar(Ce,{class:"c-dropdown-chevron"}),we(()=>Ma(F,(n(A),f(()=>n(A)?n(A).label:"Existing rules")))),d(Oe,me)},$$slots:{default:!0}});var le=p(T,2);qe.Content(le,{align:"start",side:"bottom",children:(Oe,Ne)=>{var me=ql(),F=$e(me);at(F,1,c,Dt,(Fe,R)=>{const te=Se(()=>(n(A),n(R),f(()=>{var H;return((H=n(A))==null?void 0:H.label)===n(R).label})));qe.Item(Fe,{onSelect:()=>function(H){g(A,H),n(ye)()}(n(R)),get highlight(){return n(te)},children:(H,re)=>{var de=Z();we(()=>Ie(de,(n(R),f(()=>n(R).label)))),d(H,de)},$$slots:{default:!0}})});var Ce=p(F,2),be=Fe=>{var R=Vl(),te=$e(R);qe.Separator(te,{});var H=p(te,2);qe.Label(H,{children:(re,de)=>{var Q=Z();we(()=>Ie(Q,(a(),_(c()),n(A),f(()=>{var ne;return a()!==void 0?c()[a()].description:(ne=n(A))==null?void 0:ne.description})))),d(re,Q)},$$slots:{default:!0}}),d(Fe,R)};K(Ce,Fe=>{(a()!==void 0||n(A))&&Fe(be)}),d(Oe,me)},$$slots:{default:!0}}),d(M,Y)},$$slots:{default:!0},$$legacy:!0});var y=p(B,2),k=M=>{Gs(M,{variant:"soft",color:"error",size:1,children:(se,Y)=>{var T=Z();we(()=>Ie(T,w())),d(se,T)},$$slots:{default:!0,icon:(se,Y)=>{Cr(se,{slot:"icon"})}}})};K(y,M=>{w()&&M(k)});var ae=p(y,2),L=M=>{Gs(M,{variant:"soft",color:"success",size:1,children:(se,Y)=>{var T=Z();we(()=>Ie(T,D())),d(se,T)},$$slots:{default:!0,icon:(se,Y)=>{qa(se,{slot:"icon"})}}})};K(ae,M=>{D()&&M(L)}),d(N,S)};K(b,N=>{_(c()),f(()=>c().length===0)?N(I):N(z,!1)}),d(m,j)},footer:(m,U)=>{var j=Gl(),b=h(j);He(b,{variant:"solid",color:"neutral",get disabled(){return u()},$$events:{click:P},children:(N,S)=>{var E=Z("Cancel");d(N,E)},$$slots:{default:!0}});var I=p(b,2),z=N=>{const S=Se(()=>!n(A)||u());He(N,{color:"accent",variant:"solid",get disabled(){return n(S)},get loading(){return u()},$$events:{click:x},children:(E,B)=>{var O=Z();we(()=>Ie(O,u()?"Importing...":"Import ")),d(E,O)},$$slots:{default:!0}})};K(I,N=>{_(c()),f(()=>c().length>0)&&N(z)}),d(m,j)}}}),Je(),s()}var Kl=v('<div class="c-rules-list-empty svelte-mrq2l0"><!></div>'),Yl=v('<div class="c-rule-item-info svelte-mrq2l0" slot="header-left"><div class="l-icon-wrapper svelte-mrq2l0"><!></div> <div class="c-rule-item-path svelte-mrq2l0"><!></div></div>'),Xl=v('<div class="server-actions" slot="header-right"><div class="status-controls svelte-mrq2l0"><div class="c-rules-dropdown"><!></div> <!> <!></div></div>'),Ql=v('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Create new rule file</div>'),ed=v('<div class="c-rules-actions-button-content svelte-mrq2l0"><!> Import rules <!></div>'),td=v("<!> <!>",1),sd=v("<!> <!>",1),rd=v("<!> <!>",1),ad=v(`<div class="c-rules-category svelte-mrq2l0"><div class="c-rules-section svelte-mrq2l0"><!> <div>Rules are instructions for Augment Chat and Agent that can be applied automatically across all
      conversations or referenced in specific conversations using @mentions (e.g., @rule-file.md) <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <div class="c-rules-list svelte-mrq2l0"><!></div> <div class="c-rules-actions-container svelte-mrq2l0"><!> <!></div></div> <div class="c-user-guidelines-section svelte-mrq2l0"><!> <div>User Guidelines allow you to control Augment's behavior through natural language instructions.
      These guidelines are applied globally to all Chat and Agent interactions. <a href="https://docs.augmentcode.com/setup-augment/guidelines#workspace-guidelines" target="_blank"><!></a></div> <!></div></div> <!> <!>`,1);function nd(r,e){Be(e,!1);const[t,s]=Mt(),a=()=>Xe(A,"$rulesFiles",t),l=()=>Xe(n(I),"$importFocusedIndex",t);let i=C(e,"userGuidelines",8,""),o=C(e,"userGuidelinesLengthLimit",24,()=>{}),c=C(e,"updateUserGuideline",8,()=>!1);const u=new Xr(et),w=new Ya(u),D=new Pl(et,u,w);u.registerConsumer(w);const A=w.getCachedRules(),ke=D.getShowCreateRuleDialog(),ye=D.getCreateRuleError();let x=X(!1),P=X([]),m=X(!1),U=X(""),j=X("");const b=[{label:"Auto import existing rules in the workspace",id:"auto_import",description:"Choose existing rules in your workspace to auto import to Augment."},{label:"Select file(s) or directory to import",id:"select_file_or_directory",description:"Select an existing directory or list of markdown files to import to Augment."}];let I=X(void 0),z=X(()=>{});async function N(R){try{R.id==="select_file_or_directory"?await D.selectFileToImport():R.id==="auto_import"&&await async function(){try{g(U,""),g(j,"");const te=await D.getAutoImportOptions();g(P,te.data.options),g(x,!0)}catch(te){console.error("Failed to get auto-import options:",te),g(U,"Failed to detect existing rules in workspace.")}}()}catch(te){console.error("Failed to handle import select:",te)}n(z)&&n(z)()}Kr(()=>{w.requestRules()}),We();var S=ad();ut("message",rr,function(...R){var te;(te=u.onMessageFromExtension)==null||te.apply(this,R)});var E=$e(S),B=h(E),O=h(B);ve(O,{class:"c-section-header",size:3,color:"primary",children:(R,te)=>{var H=Z("Rules");d(R,H)},$$slots:{default:!0}});var y=p(O,2),k=p(h(y)),ae=h(k);ve(ae,{size:1,weight:"regular",children:(R,te)=>{var H=Z("Learn more");d(R,H)},$$slots:{default:!0}});var L=p(y,2),M=h(L),se=R=>{var te=Kl(),H=h(te);ve(H,{size:1,color:"neutral",children:(re,de)=>{var Q=Z("No rules files found");d(re,Q)},$$slots:{default:!0}}),d(R,te)},Y=R=>{var te=tt(),H=$e(te);at(H,1,a,re=>re.path,(re,de)=>{Tt(re,{isClickable:!0,$$events:{click:()=>D.openRule(n(de).path)},$$slots:{"header-left":(Q,ne)=>{var ge=Yl(),ce=h(ge),ee=h(ce),ue=oe=>{Lt(oe,{content:"No description found",children:(G,he)=>{Cr(G,{})},$$slots:{default:!0}})},q=oe=>{Ja(oe,{})};K(ee,oe=>{n(de),_(Lr),f(()=>n(de).type===Lr.AGENT_REQUESTED&&!n(de).description)?oe(ue):oe(q,!1)});var pe=p(ce,2),J=h(pe);ve(J,{size:1,color:"neutral",children:(oe,G)=>{var he=Z();we(()=>Ie(he,(n(de),f(()=>n(de).path)))),d(oe,he)},$$slots:{default:!0}}),d(Q,ge)},"header-right":(Q,ne)=>{var ge=Xl(),ce=h(ge),ee=h(ce),ue=h(ee);hn(ue,{get rule(){return n(de)},onSave:async(J,oe)=>{await w.updateRuleContent({type:J,path:n(de).path,content:n(de).content,description:oe})}});var q=p(ee,2);He(q,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:J=>{J.stopPropagation(),D.openRule(n(de).path)}},$$slots:{iconRight:(J,oe)=>{Ha(J,{slot:"iconRight"})}}});var pe=p(q,2);He(pe,{size:1,variant:"ghost-block",color:"neutral",class:"c-rule-item-button",$$events:{click:J=>{J.stopPropagation(),D.deleteRule(n(de).path)}},$$slots:{iconRight:(J,oe)=>{Ba(J,{slot:"iconRight"})}}}),d(Q,ge)}}})}),d(R,te)};K(M,R=>{a(),f(()=>a().length===0)?R(se):R(Y,!1)});var T=p(L,2),le=h(T);He(le,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",$$events:{click:()=>D.createRule()},children:(R,te)=>{var H=Ql(),re=h(H);hs(re,{slot:"iconLeft"}),d(R,H)},$$slots:{default:!0}});var Oe=p(le,2);qe.Root(Oe,{get requestClose(){return n(z)},set requestClose(R){g(z,R)},get focusedIndex(){return n(I)},set focusedIndex(R){Js(g(I,R),"$importFocusedIndex",t)},children:(R,te)=>{var H=rd(),re=$e(H);qe.Trigger(re,{children:(Q,ne)=>{He(Q,{size:1,variant:"soft",color:"neutral",class:"c-rules-action-button",children:(ge,ce)=>{var ee=ed(),ue=h(ee);ra(ue,{slot:"iconLeft"});var q=p(ue,2);Ar(q,{slot:"iconRight"}),d(ge,ee)},$$slots:{default:!0}})},$$slots:{default:!0}});var de=p(re,2);qe.Content(de,{align:"start",side:"bottom",children:(Q,ne)=>{var ge=sd(),ce=$e(ge);at(ce,1,()=>b,q=>q.id,(q,pe)=>{qe.Item(q,{onSelect:()=>N(n(pe)),children:(J,oe)=>{var G=Z();we(()=>Ie(G,(n(pe),f(()=>n(pe).label)))),d(J,G)},$$slots:{default:!0}})});var ee=p(ce,2),ue=q=>{var pe=td(),J=$e(pe);qe.Separator(J,{});var oe=p(J,2);qe.Label(oe,{children:(G,he)=>{var xe=Z();we(()=>Ie(xe,(l(),f(()=>l()!==void 0?b[l()].description:b[0])))),d(G,xe)},$$slots:{default:!0}}),d(q,pe)};K(ee,q=>{l()!==void 0&&q(ue)}),d(Q,ge)},$$slots:{default:!0}}),d(R,H)},$$slots:{default:!0},$$legacy:!0});var Ne=p(B,2),me=h(Ne);ve(me,{class:"c-section-header",size:3,color:"primary",children:(R,te)=>{var H=Z("User Guidelines");d(R,H)},$$slots:{default:!0}});var F=p(me,2),Ce=p(h(F)),be=h(Ce);ve(be,{size:1,weight:"regular",children:(R,te)=>{var H=Z("Learn more");d(R,H)},$$slots:{default:!0}}),_a(p(F,2),{get userGuidelines(){return i()},get userGuidelinesLengthLimit(){return o()},updateUserGuideline:c()});var Fe=p(E,2);(function(R,te){Be(te,!1);const H=xr();let re=C(te,"show",8,!1),de=C(te,"errorMessage",8,""),Q=X(""),ne=X(void 0),ge=X(!1);function ce(){n(Q).trim()&&!n(ge)&&(g(ge,!0),H("create",n(Q).trim()))}function ee(){n(ge)||(H("cancel"),g(Q,""))}function ue(q){n(ge)||(q.key==="Enter"?(q.preventDefault(),ce()):q.key==="Escape"&&(q.preventDefault(),ee()))}Me(()=>(_(re()),n(ne)),()=>{re()&&n(ne)&&setTimeout(()=>{var q;return(q=n(ne))==null?void 0:q.focus()},100)}),Me(()=>(_(re()),_(de())),()=>{re()&&!de()||g(ge,!1)}),Me(()=>(_(re()),_(de())),()=>{re()||de()||g(Q,"")}),it(),We(),la(R,{get show(){return re()},title:"Create New Rule",ariaLabelledBy:"dialog-title",get preventBackdropClose(){return n(ge)},get preventEscapeClose(){return n(ge)},$$events:{cancel:ee,keydown:function(q){n(ge)||q.detail.key==="Enter"&&(q.detail.preventDefault(),ce())}},$$slots:{body:(q,pe)=>{var J=Fl(),oe=$e(J);ve(oe,{size:2,color:"secondary",children:(Re,Ee)=>{var Ke=Z("Enter a name for the new rule file (e.g., architecture.md):");d(Re,Ke)},$$slots:{default:!0}});var G=p(oe,2);Yt(G,{placeholder:"rule-name.md",get disabled(){return n(ge)},get value(){return n(Q)},set value(Re){g(Q,Re)},get textInput(){return n(ne)},set textInput(Re){g(ne,Re)},$$events:{keydown:ue},$$legacy:!0});var he=p(G,2),xe=Re=>{Gs(Re,{variant:"soft",color:"error",size:1,children:(Ee,Ke)=>{var Ge=Z();we(()=>Ie(Ge,de())),d(Ee,Ge)},$$slots:{default:!0,icon:(Ee,Ke)=>{Cr(Ee,{slot:"icon"})}}})};K(he,Re=>{de()&&Re(xe)}),d(q,J)},footer:(q,pe)=>{var J=jl(),oe=h(J);He(oe,{variant:"solid",color:"neutral",get disabled(){return n(ge)},$$events:{click:ee},children:(xe,Re)=>{var Ee=Z("Cancel");d(xe,Ee)},$$slots:{default:!0}});var G=p(oe,2);const he=Se(()=>(n(Q),n(ge),f(()=>!n(Q).trim()||n(ge))));He(G,{variant:"solid",color:"accent",get disabled(){return n(he)},get loading(){return n(ge)},$$events:{click:ce},children:(xe,Re)=>{var Ee=Z();we(()=>Ie(Ee,n(ge)?"Creating...":"Create")),d(xe,Ee)},$$slots:{default:!0}}),d(q,J)}}}),Je()})(Fe,{get show(){return Xe(ke,"$showCreateRuleDialog",t)},get errorMessage(){return Xe(ye,"$createRuleError",t)},$$events:{create:function(R){D.handleCreateRuleWithName(R.detail)},cancel:function(){D.hideCreateRuleDialog()}}}),Wl(p(Fe,2),{get show(){return n(x)},get options(){return n(P)},get isLoading(){return n(m)},get errorMessage(){return n(U)},get successMessage(){return n(j)},$$events:{select:async function(R){const te=R.detail;try{g(m,!0),g(U,"");const H=await D.processAutoImportSelection(te);let re=`Successfully imported ${H.importedRulesCount} rule${H.importedRulesCount!==1?"s":""} from ${te.label}`;H.duplicatesCount>0&&(re+=`, ${H.duplicatesCount} duplicate${H.duplicatesCount!==1?"s":""} skipped`),H.totalAttempted>H.importedRulesCount+H.duplicatesCount&&(re+=`, ${H.totalAttempted-H.importedRulesCount-H.duplicatesCount} failed`),g(j,re),setTimeout(()=>{g(x,!1),g(j,"")},500)}catch(H){console.error("Failed to process auto-import selection:",H),g(U,"Failed to import rules. Please try again.")}finally{g(m,!1)}},cancel:function(){g(x,!1),g(U,""),g(j,"")}}}),d(r,S),Je(),s()}var id=Ct("<svg><!></svg>");function od(r,e){Be(e,!1);let t=C(e,"onSignOut",8),s=X(!1);We(),He(r,{get loading(){return n(s)},variant:"soft","data-testid":"sign-out-button",$$events:{click:function(){t()(),g(s,!0)}},children:(a,l)=>{var i=Z("Sign Out");d(a,i)},$$slots:{default:!0,iconLeft:(a,l)=>{(function(i,o){const c=us(o,["children","$$slots","$$events","$$legacy"]);var u=id();Fs(u,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...c}));var w=h(u);ar(w,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M352 146.2 462 256 352 365.8V312c0-13.3-10.7-24-24-24H208v-64h120c13.3 0 24-10.7 24-24zM512 256c0-11.5-4.6-22.5-12.7-30.6L383.2 109.6c-8.7-8.7-20.5-13.6-32.8-13.6-25.6 0-46.4 20.8-46.4 46.4V176h-96c-26.5 0-48 21.5-48 48v64c0 26.5 21.5 48 48 48h96v33.6c0 25.6 20.8 46.4 46.4 46.4 12.3 0 24.1-4.9 32.8-13.6l116.1-115.8c8.1-8.1 12.7-19.1 12.7-30.6M168 80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88C39.4 32 0 71.4 0 120v272c0 48.6 39.4 88 88 88h80c13.3 0 24-10.7 24-24s-10.7-24-24-24H88c-22.1 0-40-17.9-40-40V120c0-22.1 17.9-40 40-40z"/>',!0),d(i,u)})(a,{slot:"iconLeft"})}}}),Je()}var ld=v('<span slot="content"><!></span>');function dd(r,e){Be(e,!1);const[t,s]=Mt(),a=()=>Xe(O,"$guidelines",t),l=()=>Xe(j,"$settingsComponentSupported",t),i=()=>Xe(b,"$enableAgentMode",t),o=()=>Xe(N,"$terminalSettingsStore",t),c=X(),u=X(),w=X(),D=new ks(et),A=new Ds(et),ke=new xi(et),ye=new Xr(et),x=new ta,P=new sa(et,ye,x),m=new yr(ye),U=new Ps(ye);_s(yr.key,m),_s(Ps.key,U),_s(ks.key,D),function(T){_s(fa,T)}(P),function(T){_s(ya,T)}(A);const j=D.getSettingsComponentSupported(),b=D.getEnableAgentMode(),I=D.getEnableAgentSwarmMode(),z=D.getHasEverUsedRemoteAgent();ye.registerConsumer(D),ye.registerConsumer(A),ye.registerConsumer(ke);const N=ke.getTerminalSettings();let S=X();const E={handleMessageFromExtension:T=>!(!T.data||T.data.type!==De.navigateToSettingsSection)&&(T.data.data&&typeof T.data.data=="string"&&g(S,T.data.data),!0)};ye.registerConsumer(E);const B=D.getDisplayableTools(),O=D.getGuidelines();function y(T){const le=T.trim();return!(n(u)&&le.length>n(u))&&(D.updateLocalUserGuidelines(le),et.postMessage({type:De.updateUserGuidelines,data:T}),!0)}function k(T){et.postMessage({type:De.toolConfigStartOAuth,data:{authUrl:T}}),D.startPolling()}async function ae(T){await P.openConfirmationModal({title:"Revoke Access",message:`Are you sure you want to revoke access for ${T.displayName}? This will disconnect the tool and you'll need to reconnect it to use it again.`,confirmButtonText:"Revoke Access",cancelButtonText:"Cancel"})&&et.postMessage({type:De.toolConfigRevokeAccess,data:{toolId:T.identifier}})}function L(T){ke.updateSelectedShell(T)}function M(T){ke.updateStartupScript(T)}function se(T,le){et.postMessage({type:De.toolApprovalConfigSetRequest,data:{toolId:T,approvalConfig:le}})}function Y(){et.postMessage({type:De.signOut})}Yr(()=>{D.dispose(),m.dispose(),U.dispose()}),D.notifyLoaded(),et.postMessage({type:De.getOrientationStatus}),et.postMessage({type:De.settingsPanelLoaded}),Me(()=>a(),()=>{var T;g(c,(T=a().userGuidelines)==null?void 0:T.contents)}),Me(()=>a(),()=>{var T;g(u,(T=a().userGuidelines)==null?void 0:T.lengthLimit)}),Me(()=>(l(),Pr),()=>{g(w,[l().remoteTools?ws("Tools","",oo,"section-tools"):void 0,l().userGuidelines&&!l().rules?ws("User Guidelines","Guidelines for Augment Chat to follow.",zl,"guidelines"):void 0,l().rules?ws("Rules and User Guidelines","",Ll,"guidelines"):void 0,l().workspaceContext?ws("Context","",co,"context"):void 0,ws("Account","Manage your Augment account settings.",Pr,"account")].filter(Boolean))}),Me(()=>(n(w),n(S)),()=>{var T;n(w).length>1&&!n(S)&&g(S,(T=n(w)[0])==null?void 0:T.id)}),it(),We(),ut("message",rr,function(...T){var le;(le=ye.onMessageFromExtension)==null||le.apply(this,T)}),cn.Root(r,{children:(T,le)=>{no(T,{get items(){return n(w)},mode:"tree",class:"c-settings-navigation",get selectedId(){return n(S)},$$slots:{content:(Oe,Ne)=>{var me=ld();const F=Se(()=>Ne.item);var Ce=h(me),be=R=>{},Fe=(R,te)=>{var H=de=>{Vi(de,{})},re=(de,Q)=>{var ne=ce=>{var ee=tt(),ue=$e(ee),q=J=>{nd(J,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},updateUserGuideline:y})},pe=J=>{_a(J,{get userGuidelines(){return n(c)},get userGuidelinesLengthLimit(){return n(u)},updateUserGuideline:y})};K(ue,J=>{l(),f(()=>l().rules)?J(q):J(pe,!1)}),d(ce,ee)},ge=(ce,ee)=>{var ue=pe=>{od(pe,{onSignOut:Y})},q=pe=>{const J=Se(()=>(i(),l(),f(()=>i()&&l().mcpServerList))),oe=Se(()=>(i(),l(),f(()=>i()&&l().mcpServerImport)));El(pe,{get tools(){return Xe(B,"$displayableTools",t)},onAuthenticate:k,onRevokeAccess:ae,onToolApprovalConfigChange:se,onMCPServerAdd:G=>A.addServer(G),onMCPServerSave:G=>A.updateServer(G),onMCPServerDelete:G=>A.deleteServer(G),onMCPServerToggleDisable:G=>A.toggleDisabledServer(G),onMCPServerJSONImport:G=>A.importServersFromJSON(G),get isMCPEnabled(){return n(J)},get isMCPImportEnabled(){return n(oe)},get supportedShells(){return o(),f(()=>o().supportedShells)},get selectedShell(){return o(),f(()=>o().selectedShell)},get startupScript(){return o(),f(()=>o().startupScript)},onShellSelect:L,onStartupScriptChange:M,get isTerminalEnabled(){return l(),f(()=>l().terminal)},isSoundCategoryEnabled:!0,get isAgentCategoryEnabled(){return i()},get isSwarmModeFeatureFlagEnabled(){return Xe(I,"$enableAgentSwarmMode",t)},get hasEverUsedRemoteAgent(){return Xe(z,"$hasEverUsedRemoteAgent",t)}})};K(ce,pe=>{_(n(F)),f(()=>{var J;return((J=n(F))==null?void 0:J.id)==="account"})?pe(ue):pe(q,!1)},ee)};K(de,ce=>{_(n(F)),f(()=>{var ee;return((ee=n(F))==null?void 0:ee.id)==="guidelines"})?ce(ne):ce(ge,!1)},Q)};K(R,de=>{_(n(F)),f(()=>{var Q;return((Q=n(F))==null?void 0:Q.id)==="context"})?de(H):de(re,!1)},te)};K(Ce,R=>{_(kr),_(n(F)),f(()=>!kr(n(F)))?R(be):R(Fe,!1)}),d(Oe,me)}}})},$$slots:{default:!0}}),Je(),s()}(async function(){et&&et.initialize&&await et.initialize(),Aa(dd,{target:document.getElementById("app")})})();
