var tr=Object.defineProperty;var er=(e,t,n)=>t in e?tr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var M=(e,t,n)=>er(e,typeof t!="symbol"?t+"":t,n);import{b2 as nr,b3 as rr,x as ue,y as or,v as Tn,w as _n,aA as ir,A as te,C as A,W as jt,_ as Ie,D as Oe,F as he,G as Ft,N as Ct,b as ct,I as ee,K as dt,u as Cn,J as Rt,ap as me,O as ge,aa as Dn,t as Jt,R as X,aD as ar,a5 as sr,ak as Ce,a2 as Se,a3 as Je,P as Ze,a1 as cr,z as ur,al as pr,H as He,Z as st,m as de,M as fr,X as ve,L as Ge,Q as lr,T as dr,S as vr,V as hr,l as Qe,a as tn,$ as mr}from"./SpinnerAugment-C3d3R_8C.js";import{a as An,b as rt}from"./IconButtonAugment-BYROpfM6.js";function ye(e,t,n){var r=nr(e,t);r&&r.set&&(e[t]=n,rr(()=>{e[t]=null}))}function en(e){return function(...t){return t[0].stopPropagation(),e==null?void 0:e.apply(this,t)}}function mo(e){return function(...t){return t[0].preventDefault(),e==null?void 0:e.apply(this,t)}}var nn=NaN,gr="[object Symbol]",yr=/^\s+|\s+$/g,br=/^[-+]0x[0-9a-f]+$/i,wr=/^0b[01]+$/i,xr=/^0o[0-7]+$/i,Er=parseInt,Or=typeof ue=="object"&&ue&&ue.Object===Object&&ue,Tr=typeof self=="object"&&self&&self.Object===Object&&self,_r=Or||Tr||Function("return this")(),Cr=Object.prototype.toString,Dr=Math.max,Ar=Math.min,De=function(){return _r.Date.now()};function Pe(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function rn(e){if(typeof e=="number")return e;if(function(r){return typeof r=="symbol"||function(s){return!!s&&typeof s=="object"}(r)&&Cr.call(r)==gr}(e))return nn;if(Pe(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Pe(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(yr,"");var n=wr.test(e);return n||xr.test(e)?Er(e.slice(2),n?2:8):br.test(e)?nn:+e}const on=or(function(e,t,n){var r,s,i,a,c,p,v=0,d=!1,l=!1,O=!0;if(typeof e!="function")throw new TypeError("Expected a function");function b(h){var o=r,m=s;return r=s=void 0,v=h,a=e.apply(m,o)}function E(h){var o=h-p;return p===void 0||o>=t||o<0||l&&h-v>=i}function w(){var h=De();if(E(h))return g(h);c=setTimeout(w,function(o){var m=t-(o-p);return l?Ar(m,i-(o-v)):m}(h))}function g(h){return c=void 0,O&&r?b(h):(r=s=void 0,a)}function C(){var h=De(),o=E(h);if(r=arguments,s=this,p=h,o){if(c===void 0)return function(m){return v=m,c=setTimeout(w,t),d?b(m):a}(p);if(l)return c=setTimeout(w,t),b(p)}return c===void 0&&(c=setTimeout(w,t)),a}return t=rn(t)||0,Pe(n)&&(d=!!n.leading,i=(l="maxWait"in n)?Dr(rn(n.maxWait)||0,t):i,O="trailing"in n?!!n.trailing:O),C.cancel=function(){c!==void 0&&clearTimeout(c),v=0,r=p=s=c=void 0},C.flush=function(){return c===void 0?a:g(De())},C});var K="top",it="bottom",at="right",J="left",Re="auto",ne=[K,it,at,J],kt="start",Zt="end",Lr="clippingParents",Ln="viewport",qt="popper",jr="reference",an=ne.reduce(function(e,t){return e.concat([t+"-"+kt,t+"-"+Zt])},[]),jn=[].concat(ne,[Re]).reduce(function(e,t){return e.concat([t,t+"-"+kt,t+"-"+Zt])},[]),kr=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"];function mt(e){return e?(e.nodeName||"").toLowerCase():null}function G(e){if(e==null)return window;if(e.toString()!=="[object Window]"){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function Dt(e){return e instanceof G(e).Element||e instanceof Element}function ot(e){return e instanceof G(e).HTMLElement||e instanceof HTMLElement}function Ue(e){return typeof ShadowRoot<"u"&&(e instanceof G(e).ShadowRoot||e instanceof ShadowRoot)}const kn={name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(n){var r=t.styles[n]||{},s=t.attributes[n]||{},i=t.elements[n];ot(i)&&mt(i)&&(Object.assign(i.style,r),Object.keys(s).forEach(function(a){var c=s[a];c===!1?i.removeAttribute(a):i.setAttribute(a,c===!0?"":c)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(r){var s=t.elements[r],i=t.attributes[r]||{},a=Object.keys(t.styles.hasOwnProperty(r)?t.styles[r]:n[r]).reduce(function(c,p){return c[p]="",c},{});ot(s)&&mt(s)&&(Object.assign(s.style,a),Object.keys(i).forEach(function(c){s.removeAttribute(c)}))})}},requires:["computeStyles"]};function ht(e){return e.split("-")[0]}var _t=Math.max,be=Math.min,Mt=Math.round;function Ne(){var e=navigator.userAgentData;return e!=null&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(t){return t.brand+"/"+t.version}).join(" "):navigator.userAgent}function Mn(){return!/^((?!chrome|android).)*safari/i.test(Ne())}function St(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);var r=e.getBoundingClientRect(),s=1,i=1;t&&ot(e)&&(s=e.offsetWidth>0&&Mt(r.width)/e.offsetWidth||1,i=e.offsetHeight>0&&Mt(r.height)/e.offsetHeight||1);var a=(Dt(e)?G(e):window).visualViewport,c=!Mn()&&n,p=(r.left+(c&&a?a.offsetLeft:0))/s,v=(r.top+(c&&a?a.offsetTop:0))/i,d=r.width/s,l=r.height/i;return{width:d,height:l,top:v,right:p+d,bottom:v+l,left:p,x:p,y:v}}function Fe(e){var t=St(e),n=e.offsetWidth,r=e.offsetHeight;return Math.abs(t.width-n)<=1&&(n=t.width),Math.abs(t.height-r)<=1&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function Sn(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Ue(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function bt(e){return G(e).getComputedStyle(e)}function Mr(e){return["table","td","th"].indexOf(mt(e))>=0}function Et(e){return((Dt(e)?e.ownerDocument:e.document)||window.document).documentElement}function Te(e){return mt(e)==="html"?e:e.assignedSlot||e.parentNode||(Ue(e)?e.host:null)||Et(e)}function sn(e){return ot(e)&&bt(e).position!=="fixed"?e.offsetParent:null}function re(e){for(var t=G(e),n=sn(e);n&&Mr(n)&&bt(n).position==="static";)n=sn(n);return n&&(mt(n)==="html"||mt(n)==="body"&&bt(n).position==="static")?t:n||function(r){var s=/firefox/i.test(Ne());if(/Trident/i.test(Ne())&&ot(r)&&bt(r).position==="fixed")return null;var i=Te(r);for(Ue(i)&&(i=i.host);ot(i)&&["html","body"].indexOf(mt(i))<0;){var a=bt(i);if(a.transform!=="none"||a.perspective!=="none"||a.contain==="paint"||["transform","perspective"].indexOf(a.willChange)!==-1||s&&a.willChange==="filter"||s&&a.filter&&a.filter!=="none")return i;i=i.parentNode}return null}(e)||t}function ze(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function It(e,t,n){return _t(e,be(t,n))}function Hn(e){return Object.assign({},{top:0,right:0,bottom:0,left:0},e)}function Pn(e,t){return t.reduce(function(n,r){return n[r]=e,n},{})}const Sr={name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,s=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,c=ht(n.placement),p=ze(c),v=[J,at].indexOf(c)>=0?"height":"width";if(i&&a){var d=function(k,j){return Hn(typeof(k=typeof k=="function"?k(Object.assign({},j.rects,{placement:j.placement})):k)!="number"?k:Pn(k,ne))}(s.padding,n),l=Fe(i),O=p==="y"?K:J,b=p==="y"?it:at,E=n.rects.reference[v]+n.rects.reference[p]-a[p]-n.rects.popper[v],w=a[p]-n.rects.reference[p],g=re(i),C=g?p==="y"?g.clientHeight||0:g.clientWidth||0:0,h=E/2-w/2,o=d[O],m=C-l[v]-d[b],f=C/2-l[v]/2+h,x=It(o,f,m),_=p;n.modifiersData[r]=((t={})[_]=x,t.centerOffset=x-f,t)}},effect:function(e){var t=e.state,n=e.options.element,r=n===void 0?"[data-popper-arrow]":n;r!=null&&(typeof r!="string"||(r=t.elements.popper.querySelector(r)))&&Sn(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function Ht(e){return e.split("-")[1]}var Hr={top:"auto",right:"auto",bottom:"auto",left:"auto"};function cn(e){var t,n=e.popper,r=e.popperRect,s=e.placement,i=e.variation,a=e.offsets,c=e.position,p=e.gpuAcceleration,v=e.adaptive,d=e.roundOffsets,l=e.isFixed,O=a.x,b=O===void 0?0:O,E=a.y,w=E===void 0?0:E,g=typeof d=="function"?d({x:b,y:w}):{x:b,y:w};b=g.x,w=g.y;var C=a.hasOwnProperty("x"),h=a.hasOwnProperty("y"),o=J,m=K,f=window;if(v){var x=re(n),_="clientHeight",k="clientWidth";x===G(n)&&bt(x=Et(n)).position!=="static"&&c==="absolute"&&(_="scrollHeight",k="scrollWidth"),(s===K||(s===J||s===at)&&i===Zt)&&(m=it,w-=(l&&x===f&&f.visualViewport?f.visualViewport.height:x[_])-r.height,w*=p?1:-1),(s===J||(s===K||s===it)&&i===Zt)&&(o=at,b-=(l&&x===f&&f.visualViewport?f.visualViewport.width:x[k])-r.width,b*=p?1:-1)}var j,P=Object.assign({position:c},v&&Hr),H=d===!0?function(W,$){var I=W.x,F=W.y,S=$.devicePixelRatio||1;return{x:Mt(I*S)/S||0,y:Mt(F*S)/S||0}}({x:b,y:w},G(n)):{x:b,y:w};return b=H.x,w=H.y,p?Object.assign({},P,((j={})[m]=h?"0":"",j[o]=C?"0":"",j.transform=(f.devicePixelRatio||1)<=1?"translate("+b+"px, "+w+"px)":"translate3d("+b+"px, "+w+"px, 0)",j)):Object.assign({},P,((t={})[m]=h?w+"px":"",t[o]=C?b+"px":"",t.transform="",t))}var pe={passive:!0},Pr={left:"right",right:"left",bottom:"top",top:"bottom"};function fe(e){return e.replace(/left|right|bottom|top/g,function(t){return Pr[t]})}var Rr={start:"end",end:"start"};function un(e){return e.replace(/start|end/g,function(t){return Rr[t]})}function Xe(e){var t=G(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function $e(e){return St(Et(e)).left+Xe(e).scrollLeft}function Ye(e){var t=bt(e),n=t.overflow,r=t.overflowX,s=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+s+r)}function Rn(e){return["html","body","#document"].indexOf(mt(e))>=0?e.ownerDocument.body:ot(e)&&Ye(e)?e:Rn(Te(e))}function zt(e,t){var n;t===void 0&&(t=[]);var r=Rn(e),s=r===((n=e.ownerDocument)==null?void 0:n.body),i=G(r),a=s?[i].concat(i.visualViewport||[],Ye(r)?r:[]):r,c=t.concat(a);return s?c:c.concat(zt(Te(a)))}function We(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function pn(e,t,n){return t===Ln?We(function(r,s){var i=G(r),a=Et(r),c=i.visualViewport,p=a.clientWidth,v=a.clientHeight,d=0,l=0;if(c){p=c.width,v=c.height;var O=Mn();(O||!O&&s==="fixed")&&(d=c.offsetLeft,l=c.offsetTop)}return{width:p,height:v,x:d+$e(r),y:l}}(e,n)):Dt(t)?function(r,s){var i=St(r,!1,s==="fixed");return i.top=i.top+r.clientTop,i.left=i.left+r.clientLeft,i.bottom=i.top+r.clientHeight,i.right=i.left+r.clientWidth,i.width=r.clientWidth,i.height=r.clientHeight,i.x=i.left,i.y=i.top,i}(t,n):We(function(r){var s,i=Et(r),a=Xe(r),c=(s=r.ownerDocument)==null?void 0:s.body,p=_t(i.scrollWidth,i.clientWidth,c?c.scrollWidth:0,c?c.clientWidth:0),v=_t(i.scrollHeight,i.clientHeight,c?c.scrollHeight:0,c?c.clientHeight:0),d=-a.scrollLeft+$e(r),l=-a.scrollTop;return bt(c||i).direction==="rtl"&&(d+=_t(i.clientWidth,c?c.clientWidth:0)-p),{width:p,height:v,x:d,y:l}}(Et(e)))}function Nr(e,t,n,r){var s=t==="clippingParents"?function(p){var v=zt(Te(p)),d=["absolute","fixed"].indexOf(bt(p).position)>=0&&ot(p)?re(p):p;return Dt(d)?v.filter(function(l){return Dt(l)&&Sn(l,d)&&mt(l)!=="body"}):[]}(e):[].concat(t),i=[].concat(s,[n]),a=i[0],c=i.reduce(function(p,v){var d=pn(e,v,r);return p.top=_t(d.top,p.top),p.right=be(d.right,p.right),p.bottom=be(d.bottom,p.bottom),p.left=_t(d.left,p.left),p},pn(e,a,r));return c.width=c.right-c.left,c.height=c.bottom-c.top,c.x=c.left,c.y=c.top,c}function Nn(e){var t,n=e.reference,r=e.element,s=e.placement,i=s?ht(s):null,a=s?Ht(s):null,c=n.x+n.width/2-r.width/2,p=n.y+n.height/2-r.height/2;switch(i){case K:t={x:c,y:n.y-r.height};break;case it:t={x:c,y:n.y+n.height};break;case at:t={x:n.x+n.width,y:p};break;case J:t={x:n.x-r.width,y:p};break;default:t={x:n.x,y:n.y}}var v=i?ze(i):null;if(v!=null){var d=v==="y"?"height":"width";switch(a){case kt:t[v]=t[v]-(n[d]/2-r[d]/2);break;case Zt:t[v]=t[v]+(n[d]/2-r[d]/2)}}return t}function Gt(e,t){t===void 0&&(t={});var n=t,r=n.placement,s=r===void 0?e.placement:r,i=n.strategy,a=i===void 0?e.strategy:i,c=n.boundary,p=c===void 0?Lr:c,v=n.rootBoundary,d=v===void 0?Ln:v,l=n.elementContext,O=l===void 0?qt:l,b=n.altBoundary,E=b!==void 0&&b,w=n.padding,g=w===void 0?0:w,C=Hn(typeof g!="number"?g:Pn(g,ne)),h=O===qt?jr:qt,o=e.rects.popper,m=e.elements[E?h:O],f=Nr(Dt(m)?m:m.contextElement||Et(e.elements.popper),p,d,a),x=St(e.elements.reference),_=Nn({reference:x,element:o,placement:s}),k=We(Object.assign({},o,_)),j=O===qt?k:x,P={top:f.top-j.top+C.top,bottom:j.bottom-f.bottom+C.bottom,left:f.left-j.left+C.left,right:j.right-f.right+C.right},H=e.modifiersData.offset;if(O===qt&&H){var W=H[s];Object.keys(P).forEach(function($){var I=[at,it].indexOf($)>=0?1:-1,F=[K,it].indexOf($)>=0?"y":"x";P[$]+=W[F]*I})}return P}function $r(e,t){t===void 0&&(t={});var n=t,r=n.placement,s=n.boundary,i=n.rootBoundary,a=n.padding,c=n.flipVariations,p=n.allowedAutoPlacements,v=p===void 0?jn:p,d=Ht(r),l=d?c?an:an.filter(function(E){return Ht(E)===d}):ne,O=l.filter(function(E){return v.indexOf(E)>=0});O.length===0&&(O=l);var b=O.reduce(function(E,w){return E[w]=Gt(e,{placement:w,boundary:s,rootBoundary:i,padding:a})[ht(w)],E},{});return Object.keys(b).sort(function(E,w){return b[E]-b[w]})}const Wr={name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var s=n.mainAxis,i=s===void 0||s,a=n.altAxis,c=a===void 0||a,p=n.fallbackPlacements,v=n.padding,d=n.boundary,l=n.rootBoundary,O=n.altBoundary,b=n.flipVariations,E=b===void 0||b,w=n.allowedAutoPlacements,g=t.options.placement,C=ht(g),h=p||(C===g||!E?[fe(g)]:function(R){if(ht(R)===Re)return[];var V=fe(R);return[un(R),V,un(V)]}(g)),o=[g].concat(h).reduce(function(R,V){return R.concat(ht(V)===Re?$r(t,{placement:V,boundary:d,rootBoundary:l,padding:v,flipVariations:E,allowedAutoPlacements:w}):V)},[]),m=t.rects.reference,f=t.rects.popper,x=new Map,_=!0,k=o[0],j=0;j<o.length;j++){var P=o[j],H=ht(P),W=Ht(P)===kt,$=[K,it].indexOf(H)>=0,I=$?"width":"height",F=Gt(t,{placement:P,boundary:d,rootBoundary:l,altBoundary:O,padding:v}),S=$?W?at:J:W?it:K;m[I]>f[I]&&(S=fe(S));var N=fe(S),Q=[];if(i&&Q.push(F[H]<=0),c&&Q.push(F[S]<=0,F[N]<=0),Q.every(function(R){return R})){k=P,_=!1;break}x.set(P,Q)}if(_)for(var tt=function(R){var V=o.find(function(pt){var ft=x.get(pt);if(ft)return ft.slice(0,R).every(function(wt){return wt})});if(V)return k=V,"break"},et=E?3:1;et>0&&tt(et)!=="break";et--);t.placement!==k&&(t.modifiersData[r]._skip=!0,t.placement=k,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function fn(e,t,n){return n===void 0&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function ln(e){return[K,at,it,J].some(function(t){return e[t]>=0})}const Vr={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,s=n.offset,i=s===void 0?[0,0]:s,a=jn.reduce(function(d,l){return d[l]=function(O,b,E){var w=ht(O),g=[J,K].indexOf(w)>=0?-1:1,C=typeof E=="function"?E(Object.assign({},b,{placement:O})):E,h=C[0],o=C[1];return h=h||0,o=(o||0)*g,[J,at].indexOf(w)>=0?{x:o,y:h}:{x:h,y:o}}(l,t.rects,i),d},{}),c=a[t.placement],p=c.x,v=c.y;t.modifiersData.popperOffsets!=null&&(t.modifiersData.popperOffsets.x+=p,t.modifiersData.popperOffsets.y+=v),t.modifiersData[r]=a}},Br={name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,s=n.mainAxis,i=s===void 0||s,a=n.altAxis,c=a!==void 0&&a,p=n.boundary,v=n.rootBoundary,d=n.altBoundary,l=n.padding,O=n.tether,b=O===void 0||O,E=n.tetherOffset,w=E===void 0?0:E,g=Gt(t,{boundary:p,rootBoundary:v,padding:l,altBoundary:d}),C=ht(t.placement),h=Ht(t.placement),o=!h,m=ze(C),f=m==="x"?"y":"x",x=t.modifiersData.popperOffsets,_=t.rects.reference,k=t.rects.popper,j=typeof w=="function"?w(Object.assign({},t.rects,{placement:t.placement})):w,P=typeof j=="number"?{mainAxis:j,altAxis:j}:Object.assign({mainAxis:0,altAxis:0},j),H=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,W={x:0,y:0};if(x){if(i){var $,I=m==="y"?K:J,F=m==="y"?it:at,S=m==="y"?"height":"width",N=x[m],Q=N+g[I],tt=N-g[F],et=b?-k[S]/2:0,R=h===kt?_[S]:k[S],V=h===kt?-k[S]:-_[S],pt=t.elements.arrow,ft=b&&pt?Fe(pt):{width:0,height:0},wt=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},Nt=wt[I],gt=wt[F],Ot=It(0,_[S],ft[S]),oe=o?_[S]/2-et-Ot-Nt-P.mainAxis:R-Ot-Nt-P.mainAxis,ie=o?-_[S]/2+et+Ot+gt+P.mainAxis:V+Ot+gt+P.mainAxis,At=t.elements.arrow&&re(t.elements.arrow),ae=At?m==="y"?At.clientTop||0:At.clientLeft||0:0,$t=($=H==null?void 0:H[m])!=null?$:0,se=N+ie-$t,Wt=It(b?be(Q,N+oe-$t-ae):Q,N,b?_t(tt,se):tt);x[m]=Wt,W[m]=Wt-N}if(c){var Vt,Bt=m==="x"?K:J,ce=m==="x"?it:at,nt=x[f],u=f==="y"?"height":"width",y=nt+g[Bt],T=nt-g[ce],D=[K,J].indexOf(C)!==-1,L=(Vt=H==null?void 0:H[f])!=null?Vt:0,B=D?y:nt-_[u]-k[u]-L+P.altAxis,U=D?nt+_[u]+k[u]-L-P.altAxis:T,z=b&&D?function(q,yt,Y){var Z=It(q,yt,Y);return Z>Y?Y:Z}(B,nt,U):It(b?B:y,nt,b?U:T);x[f]=z,W[f]=z-nt}t.modifiersData[r]=W}},requiresIfExists:["offset"]};function qr(e,t,n){n===void 0&&(n=!1);var r,s,i=ot(t),a=ot(t)&&function(l){var O=l.getBoundingClientRect(),b=Mt(O.width)/l.offsetWidth||1,E=Mt(O.height)/l.offsetHeight||1;return b!==1||E!==1}(t),c=Et(t),p=St(e,a,n),v={scrollLeft:0,scrollTop:0},d={x:0,y:0};return(i||!i&&!n)&&((mt(t)!=="body"||Ye(c))&&(v=(r=t)!==G(r)&&ot(r)?{scrollLeft:(s=r).scrollLeft,scrollTop:s.scrollTop}:Xe(r)),ot(t)?((d=St(t,!0)).x+=t.clientLeft,d.y+=t.clientTop):c&&(d.x=$e(c))),{x:p.left+v.scrollLeft-d.x,y:p.top+v.scrollTop-d.y,width:p.width,height:p.height}}function Ir(e){var t=new Map,n=new Set,r=[];function s(i){n.add(i.name),[].concat(i.requires||[],i.requiresIfExists||[]).forEach(function(a){if(!n.has(a)){var c=t.get(a);c&&s(c)}}),r.push(i)}return e.forEach(function(i){t.set(i.name,i)}),e.forEach(function(i){n.has(i.name)||s(i)}),r}var dn={placement:"bottom",modifiers:[],strategy:"absolute"};function vn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(r){return!(r&&typeof r.getBoundingClientRect=="function")})}function Ur(e){e===void 0&&(e={});var t=e,n=t.defaultModifiers,r=n===void 0?[]:n,s=t.defaultOptions,i=s===void 0?dn:s;return function(a,c,p){p===void 0&&(p=i);var v,d,l={placement:"bottom",orderedModifiers:[],options:Object.assign({},dn,i),modifiersData:{},elements:{reference:a,popper:c},attributes:{},styles:{}},O=[],b=!1,E={state:l,setOptions:function(g){var C=typeof g=="function"?g(l.options):g;w(),l.options=Object.assign({},i,l.options,C),l.scrollParents={reference:Dt(a)?zt(a):a.contextElement?zt(a.contextElement):[],popper:zt(c)};var h,o,m=function(f){var x=Ir(f);return kr.reduce(function(_,k){return _.concat(x.filter(function(j){return j.phase===k}))},[])}((h=[].concat(r,l.options.modifiers),o=h.reduce(function(f,x){var _=f[x.name];return f[x.name]=_?Object.assign({},_,x,{options:Object.assign({},_.options,x.options),data:Object.assign({},_.data,x.data)}):x,f},{}),Object.keys(o).map(function(f){return o[f]})));return l.orderedModifiers=m.filter(function(f){return f.enabled}),l.orderedModifiers.forEach(function(f){var x=f.name,_=f.options,k=_===void 0?{}:_,j=f.effect;if(typeof j=="function"){var P=j({state:l,name:x,instance:E,options:k}),H=function(){};O.push(P||H)}}),E.update()},forceUpdate:function(){if(!b){var g=l.elements,C=g.reference,h=g.popper;if(vn(C,h)){l.rects={reference:qr(C,re(h),l.options.strategy==="fixed"),popper:Fe(h)},l.reset=!1,l.placement=l.options.placement,l.orderedModifiers.forEach(function(j){return l.modifiersData[j.name]=Object.assign({},j.data)});for(var o=0;o<l.orderedModifiers.length;o++)if(l.reset!==!0){var m=l.orderedModifiers[o],f=m.fn,x=m.options,_=x===void 0?{}:x,k=m.name;typeof f=="function"&&(l=f({state:l,options:_,name:k,instance:E})||l)}else l.reset=!1,o=-1}}},update:(v=function(){return new Promise(function(g){E.forceUpdate(),g(l)})},function(){return d||(d=new Promise(function(g){Promise.resolve().then(function(){d=void 0,g(v())})})),d}),destroy:function(){w(),b=!0}};if(!vn(a,c))return E;function w(){O.forEach(function(g){return g()}),O=[]}return E.setOptions(p).then(function(g){!b&&p.onFirstUpdate&&p.onFirstUpdate(g)}),E}}var Fr=Ur({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,s=r.scroll,i=s===void 0||s,a=r.resize,c=a===void 0||a,p=G(t.elements.popper),v=[].concat(t.scrollParents.reference,t.scrollParents.popper);return i&&v.forEach(function(d){d.addEventListener("scroll",n.update,pe)}),c&&p.addEventListener("resize",n.update,pe),function(){i&&v.forEach(function(d){d.removeEventListener("scroll",n.update,pe)}),c&&p.removeEventListener("resize",n.update,pe)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=Nn({reference:t.rects.reference,element:t.rects.popper,placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,s=r===void 0||r,i=n.adaptive,a=i===void 0||i,c=n.roundOffsets,p=c===void 0||c,v={placement:ht(t.placement),variation:Ht(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:s,isFixed:t.options.strategy==="fixed"};t.modifiersData.popperOffsets!=null&&(t.styles.popper=Object.assign({},t.styles.popper,cn(Object.assign({},v,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:a,roundOffsets:p})))),t.modifiersData.arrow!=null&&(t.styles.arrow=Object.assign({},t.styles.arrow,cn(Object.assign({},v,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:p})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},kn,Vr,Wr,Br,Sr,{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,s=t.rects.popper,i=t.modifiersData.preventOverflow,a=Gt(t,{elementContext:"reference"}),c=Gt(t,{altBoundary:!0}),p=fn(a,r),v=fn(c,s,i),d=ln(p),l=ln(v);t.modifiersData[n]={referenceClippingOffsets:p,popperEscapeOffsets:v,isReferenceHidden:d,hasPopperEscaped:l},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":d,"data-popper-escaped":l})}}]}),$n="tippy-content",zr="tippy-backdrop",Wn="tippy-arrow",Vn="tippy-svg-arrow",Tt={passive:!0,capture:!0},Bn=function(){return document.body};function Ae(e,t,n){if(Array.isArray(e)){var r=e[t];return r??(Array.isArray(n)?n[t]:n)}return e}function Ke(e,t){var n={}.toString.call(e);return n.indexOf("[object")===0&&n.indexOf(t+"]")>-1}function qn(e,t){return typeof e=="function"?e.apply(void 0,t):e}function hn(e,t){return t===0?e:function(r){clearTimeout(n),n=setTimeout(function(){e(r)},t)};var n}function Lt(e){return[].concat(e)}function mn(e,t){e.indexOf(t)===-1&&e.push(t)}function we(e){return[].slice.call(e)}function gn(e){return Object.keys(e).reduce(function(t,n){return e[n]!==void 0&&(t[n]=e[n]),t},{})}function Xt(){return document.createElement("div")}function _e(e){return["Element","Fragment"].some(function(t){return Ke(e,t)})}function Xr(e){return _e(e)?[e]:function(t){return Ke(t,"NodeList")}(e)?we(e):Array.isArray(e)?e:we(document.querySelectorAll(e))}function Le(e,t){e.forEach(function(n){n&&(n.style.transitionDuration=t+"ms")})}function yn(e,t){e.forEach(function(n){n&&n.setAttribute("data-state",t)})}function je(e,t,n){var r=t+"EventListener";["transitionend","webkitTransitionEnd"].forEach(function(s){e[r](s,n)})}function bn(e,t){for(var n=t;n;){var r;if(e.contains(n))return!0;n=n.getRootNode==null||(r=n.getRootNode())==null?void 0:r.host}return!1}var vt={isTouch:!1},wn=0;function Yr(){vt.isTouch||(vt.isTouch=!0,window.performance&&document.addEventListener("mousemove",In))}function In(){var e=performance.now();e-wn<20&&(vt.isTouch=!1,document.removeEventListener("mousemove",In)),wn=e}function Kr(){var e,t=document.activeElement;if((e=t)&&e._tippy&&e._tippy.reference===e){var n=t._tippy;t.blur&&!n.state.isVisible&&t.blur()}}var Jr=typeof window<"u"&&typeof document<"u"&&!!window.msCrypto,ut=Object.assign({appendTo:Bn,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),Zr=Object.keys(ut);function Un(e){var t=(e.plugins||[]).reduce(function(n,r){var s,i=r.name,a=r.defaultValue;return i&&(n[i]=e[i]!==void 0?e[i]:(s=ut[i])!=null?s:a),n},{});return Object.assign({},e,t)}function xn(e,t){var n=Object.assign({},t,{content:qn(t.content,[e])},t.ignoreAttributes?{}:function(r,s){return(s?Object.keys(Un(Object.assign({},ut,{plugins:s}))):Zr).reduce(function(i,a){var c=(r.getAttribute("data-tippy-"+a)||"").trim();if(!c)return i;if(a==="content")i[a]=c;else try{i[a]=JSON.parse(c)}catch{i[a]=c}return i},{})}(e,t.plugins));return n.aria=Object.assign({},ut.aria,n.aria),n.aria={expanded:n.aria.expanded==="auto"?t.interactive:n.aria.expanded,content:n.aria.content==="auto"?t.interactive?null:"describedby":n.aria.content},n}var Gr=function(){return"innerHTML"};function Ve(e,t){e[Gr()]=t}function En(e){var t=Xt();return e===!0?t.className=Wn:(t.className=Vn,_e(e)?t.appendChild(e):Ve(t,e)),t}function On(e,t){_e(t.content)?(Ve(e,""),e.appendChild(t.content)):typeof t.content!="function"&&(t.allowHTML?Ve(e,t.content):e.textContent=t.content)}function Be(e){var t=e.firstElementChild,n=we(t.children);return{box:t,content:n.find(function(r){return r.classList.contains($n)}),arrow:n.find(function(r){return r.classList.contains(Wn)||r.classList.contains(Vn)}),backdrop:n.find(function(r){return r.classList.contains(zr)})}}function Fn(e){var t=Xt(),n=Xt();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var r=Xt();function s(i,a){var c=Be(t),p=c.box,v=c.content,d=c.arrow;a.theme?p.setAttribute("data-theme",a.theme):p.removeAttribute("data-theme"),typeof a.animation=="string"?p.setAttribute("data-animation",a.animation):p.removeAttribute("data-animation"),a.inertia?p.setAttribute("data-inertia",""):p.removeAttribute("data-inertia"),p.style.maxWidth=typeof a.maxWidth=="number"?a.maxWidth+"px":a.maxWidth,a.role?p.setAttribute("role",a.role):p.removeAttribute("role"),i.content===a.content&&i.allowHTML===a.allowHTML||On(v,e.props),a.arrow?d?i.arrow!==a.arrow&&(p.removeChild(d),p.appendChild(En(a.arrow))):p.appendChild(En(a.arrow)):d&&p.removeChild(d)}return r.className=$n,r.setAttribute("data-state","hidden"),On(r,e.props),t.appendChild(n),n.appendChild(r),s(e.props,e.props),{popper:t,onUpdate:s}}Fn.$$tippy=!0;var Qr=1,le=[],ke=[];function to(e,t){var n,r,s,i,a,c,p,v,d=xn(e,Object.assign({},ut,Un(gn(t)))),l=!1,O=!1,b=!1,E=!1,w=[],g=hn(At,d.interactiveDebounce),C=Qr++,h=(v=d.plugins).filter(function(u,y){return v.indexOf(u)===y}),o={id:C,reference:e,popper:Xt(),popperInstance:null,props:d,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:h,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(r),cancelAnimationFrame(s)},setProps:function(u){if(!o.state.isDestroyed){N("onBeforeUpdate",[o,u]),oe();var y=o.props,T=xn(e,Object.assign({},y,gn(u),{ignoreAttributes:!0}));o.props=T,Ot(),y.interactiveDebounce!==T.interactiveDebounce&&(et(),g=hn(At,T.interactiveDebounce)),y.triggerTarget&&!T.triggerTarget?Lt(y.triggerTarget).forEach(function(D){D.removeAttribute("aria-expanded")}):T.triggerTarget&&e.removeAttribute("aria-expanded"),tt(),S(),x&&x(y,T),o.popperInstance&&(Wt(),Bt().forEach(function(D){requestAnimationFrame(D._tippy.popperInstance.forceUpdate)})),N("onAfterUpdate",[o,u])}},setContent:function(u){o.setProps({content:u})},show:function(){var u=o.state.isVisible,y=o.state.isDestroyed,T=!o.state.isEnabled,D=vt.isTouch&&!o.props.touch,L=Ae(o.props.duration,0,ut.duration);if(!(u||y||T||D)&&!W().hasAttribute("disabled")&&(N("onShow",[o],!1),o.props.onShow(o)!==!1)){if(o.state.isVisible=!0,H()&&(f.style.visibility="visible"),S(),ft(),o.state.isMounted||(f.style.transition="none"),H()){var B=I();Le([B.box,B.content],0)}c=function(){var U;if(o.state.isVisible&&!E){if(E=!0,f.offsetHeight,f.style.transition=o.props.moveTransition,H()&&o.props.animation){var z=I(),q=z.box,yt=z.content;Le([q,yt],L),yn([q,yt],"visible")}Q(),tt(),mn(ke,o),(U=o.popperInstance)==null||U.forceUpdate(),N("onMount",[o]),o.props.animation&&H()&&function(Y,Z){Nt(Y,Z)}(L,function(){o.state.isShown=!0,N("onShown",[o])})}},function(){var U,z=o.props.appendTo,q=W();U=o.props.interactive&&z===Bn||z==="parent"?q.parentNode:qn(z,[q]),U.contains(f)||U.appendChild(f),o.state.isMounted=!0,Wt()}()}},hide:function(){var u=!o.state.isVisible,y=o.state.isDestroyed,T=!o.state.isEnabled,D=Ae(o.props.duration,1,ut.duration);if(!(u||y||T)&&(N("onHide",[o],!1),o.props.onHide(o)!==!1)){if(o.state.isVisible=!1,o.state.isShown=!1,E=!1,l=!1,H()&&(f.style.visibility="hidden"),et(),wt(),S(!0),H()){var L=I(),B=L.box,U=L.content;o.props.animation&&(Le([B,U],D),yn([B,U],"hidden"))}Q(),tt(),o.props.animation?H()&&function(z,q){Nt(z,function(){!o.state.isVisible&&f.parentNode&&f.parentNode.contains(f)&&q()})}(D,o.unmount):o.unmount()}},hideWithInteractivity:function(u){$().addEventListener("mousemove",g),mn(le,g),g(u)},enable:function(){o.state.isEnabled=!0},disable:function(){o.hide(),o.state.isEnabled=!1},unmount:function(){o.state.isVisible&&o.hide(),o.state.isMounted&&(Vt(),Bt().forEach(function(u){u._tippy.unmount()}),f.parentNode&&f.parentNode.removeChild(f),ke=ke.filter(function(u){return u!==o}),o.state.isMounted=!1,N("onHidden",[o]))},destroy:function(){o.state.isDestroyed||(o.clearDelayTimeouts(),o.unmount(),oe(),delete e._tippy,o.state.isDestroyed=!0,N("onDestroy",[o]))}};if(!d.render)return o;var m=d.render(o),f=m.popper,x=m.onUpdate;f.setAttribute("data-tippy-root",""),f.id="tippy-"+o.id,o.popper=f,e._tippy=o,f._tippy=o;var _=h.map(function(u){return u.fn(o)}),k=e.hasAttribute("aria-expanded");return Ot(),tt(),S(),N("onCreate",[o]),d.showOnCreate&&ce(),f.addEventListener("mouseenter",function(){o.props.interactive&&o.state.isVisible&&o.clearDelayTimeouts()}),f.addEventListener("mouseleave",function(){o.props.interactive&&o.props.trigger.indexOf("mouseenter")>=0&&$().addEventListener("mousemove",g)}),o;function j(){var u=o.props.touch;return Array.isArray(u)?u:[u,0]}function P(){return j()[0]==="hold"}function H(){var u;return!((u=o.props.render)==null||!u.$$tippy)}function W(){return p||e}function $(){var u,y,T=W().parentNode;return T&&(y=Lt(T)[0])!=null&&(u=y.ownerDocument)!=null&&u.body?y.ownerDocument:document}function I(){return Be(f)}function F(u){return o.state.isMounted&&!o.state.isVisible||vt.isTouch||i&&i.type==="focus"?0:Ae(o.props.delay,u?0:1,ut.delay)}function S(u){u===void 0&&(u=!1),f.style.pointerEvents=o.props.interactive&&!u?"":"none",f.style.zIndex=""+o.props.zIndex}function N(u,y,T){var D;T===void 0&&(T=!0),_.forEach(function(L){L[u]&&L[u].apply(L,y)}),T&&(D=o.props)[u].apply(D,y)}function Q(){var u=o.props.aria;if(u.content){var y="aria-"+u.content,T=f.id;Lt(o.props.triggerTarget||e).forEach(function(D){var L=D.getAttribute(y);if(o.state.isVisible)D.setAttribute(y,L?L+" "+T:T);else{var B=L&&L.replace(T,"").trim();B?D.setAttribute(y,B):D.removeAttribute(y)}})}}function tt(){!k&&o.props.aria.expanded&&Lt(o.props.triggerTarget||e).forEach(function(u){o.props.interactive?u.setAttribute("aria-expanded",o.state.isVisible&&u===W()?"true":"false"):u.removeAttribute("aria-expanded")})}function et(){$().removeEventListener("mousemove",g),le=le.filter(function(u){return u!==g})}function R(u){if(!vt.isTouch||!b&&u.type!=="mousedown"){var y=u.composedPath&&u.composedPath()[0]||u.target;if(!o.props.interactive||!bn(f,y)){if(Lt(o.props.triggerTarget||e).some(function(T){return bn(T,y)})){if(vt.isTouch||o.state.isVisible&&o.props.trigger.indexOf("click")>=0)return}else N("onClickOutside",[o,u]);o.props.hideOnClick===!0&&(o.clearDelayTimeouts(),o.hide(),O=!0,setTimeout(function(){O=!1}),o.state.isMounted||wt())}}}function V(){b=!0}function pt(){b=!1}function ft(){var u=$();u.addEventListener("mousedown",R,!0),u.addEventListener("touchend",R,Tt),u.addEventListener("touchstart",pt,Tt),u.addEventListener("touchmove",V,Tt)}function wt(){var u=$();u.removeEventListener("mousedown",R,!0),u.removeEventListener("touchend",R,Tt),u.removeEventListener("touchstart",pt,Tt),u.removeEventListener("touchmove",V,Tt)}function Nt(u,y){var T=I().box;function D(L){L.target===T&&(je(T,"remove",D),y())}if(u===0)return y();je(T,"remove",a),je(T,"add",D),a=D}function gt(u,y,T){T===void 0&&(T=!1),Lt(o.props.triggerTarget||e).forEach(function(D){D.addEventListener(u,y,T),w.push({node:D,eventType:u,handler:y,options:T})})}function Ot(){var u;P()&&(gt("touchstart",ie,{passive:!0}),gt("touchend",ae,{passive:!0})),(u=o.props.trigger,u.split(/\s+/).filter(Boolean)).forEach(function(y){if(y!=="manual")switch(gt(y,ie),y){case"mouseenter":gt("mouseleave",ae);break;case"focus":gt(Jr?"focusout":"blur",$t);break;case"focusin":gt("focusout",$t)}})}function oe(){w.forEach(function(u){var y=u.node,T=u.eventType,D=u.handler,L=u.options;y.removeEventListener(T,D,L)}),w=[]}function ie(u){var y,T=!1;if(o.state.isEnabled&&!se(u)&&!O){var D=((y=i)==null?void 0:y.type)==="focus";i=u,p=u.currentTarget,tt(),!o.state.isVisible&&Ke(u,"MouseEvent")&&le.forEach(function(L){return L(u)}),u.type==="click"&&(o.props.trigger.indexOf("mouseenter")<0||l)&&o.props.hideOnClick!==!1&&o.state.isVisible?T=!0:ce(u),u.type==="click"&&(l=!T),T&&!D&&nt(u)}}function At(u){var y=u.target,T=W().contains(y)||f.contains(y);u.type==="mousemove"&&T||function(D,L){var B=L.clientX,U=L.clientY;return D.every(function(z){var q=z.popperRect,yt=z.popperState,Y=z.props.interactiveBorder,Z=yt.placement.split("-")[0],lt=yt.modifiersData.offset;if(!lt)return!0;var zn=Z==="bottom"?lt.top.y:0,Xn=Z==="top"?lt.bottom.y:0,Yn=Z==="right"?lt.left.x:0,Kn=Z==="left"?lt.right.x:0,Jn=q.top-U+zn>Y,Zn=U-q.bottom-Xn>Y,Gn=q.left-B+Yn>Y,Qn=B-q.right-Kn>Y;return Jn||Zn||Gn||Qn})}(Bt().concat(f).map(function(D){var L,B=(L=D._tippy.popperInstance)==null?void 0:L.state;return B?{popperRect:D.getBoundingClientRect(),popperState:B,props:d}:null}).filter(Boolean),u)&&(et(),nt(u))}function ae(u){se(u)||o.props.trigger.indexOf("click")>=0&&l||(o.props.interactive?o.hideWithInteractivity(u):nt(u))}function $t(u){o.props.trigger.indexOf("focusin")<0&&u.target!==W()||o.props.interactive&&u.relatedTarget&&f.contains(u.relatedTarget)||nt(u)}function se(u){return!!vt.isTouch&&P()!==u.type.indexOf("touch")>=0}function Wt(){Vt();var u=o.props,y=u.popperOptions,T=u.placement,D=u.offset,L=u.getReferenceClientRect,B=u.moveTransition,U=H()?Be(f).arrow:null,z=L?{getBoundingClientRect:L,contextElement:L.contextElement||W()}:e,q=[{name:"offset",options:{offset:D}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!B}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(yt){var Y=yt.state;if(H()){var Z=I().box;["placement","reference-hidden","escaped"].forEach(function(lt){lt==="placement"?Z.setAttribute("data-placement",Y.placement):Y.attributes.popper["data-popper-"+lt]?Z.setAttribute("data-"+lt,""):Z.removeAttribute("data-"+lt)}),Y.attributes.popper={}}}}];H()&&U&&q.push({name:"arrow",options:{element:U,padding:3}}),q.push.apply(q,(y==null?void 0:y.modifiers)||[]),o.popperInstance=Fr(z,f,Object.assign({},y,{placement:T,onFirstUpdate:c,modifiers:q}))}function Vt(){o.popperInstance&&(o.popperInstance.destroy(),o.popperInstance=null)}function Bt(){return we(f.querySelectorAll("[data-tippy-root]"))}function ce(u){o.clearDelayTimeouts(),u&&N("onTrigger",[o,u]),ft();var y=F(!0),T=j(),D=T[0],L=T[1];vt.isTouch&&D==="hold"&&L&&(y=L),y?n=setTimeout(function(){o.show()},y):o.show()}function nt(u){if(o.clearDelayTimeouts(),N("onUntrigger",[o,u]),o.state.isVisible){if(!(o.props.trigger.indexOf("mouseenter")>=0&&o.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(u.type)>=0&&l)){var y=F(!1);y?r=setTimeout(function(){o.state.isVisible&&o.hide()},y):s=requestAnimationFrame(function(){o.hide()})}}else wt()}}function Ut(e,t){t===void 0&&(t={});var n=ut.plugins.concat(t.plugins||[]);document.addEventListener("touchstart",Yr,Tt),window.addEventListener("blur",Kr);var r=Object.assign({},t,{plugins:n}),s=Xr(e).reduce(function(i,a){var c=a&&to(a,r);return c&&i.push(c),i},[]);return _e(e)?s[0]:s}Ut.defaultProps=ut,Ut.setDefaultProps=function(e){Object.keys(e).forEach(function(t){ut[t]=e[t]})},Ut.currentInput=vt,Object.assign({},kn,{effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow)}}),Ut.setDefaultProps({render:Fn});var Qt=(e=>(e.Hover="hover",e.Click="click",e))(Qt||{});const Yt=class Yt extends Event{constructor(){super(Yt.eventType,{bubbles:!0})}static isEvent(t){return t.type===Yt.eventType}};M(Yt,"eventType","augment-ds-event__close-tooltip-request");let xt=Yt;const Ee=class Ee{constructor(t){M(this,"debouncedHoverStart");M(this,"debouncedHoverEnd");M(this,"handleMouseEnter",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});M(this,"handleMouseLeave",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.call(this)});M(this,"handleMouseMove",()=>{var t,n;(t=this.debouncedHoverEnd)==null||t.cancel(),(n=this.debouncedHoverStart)==null||n.call(this)});M(this,"cancelHovers",()=>{var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()});this.debouncedHoverStart=on(t.onHoverStart,t.hoverTriggerDuration),this.debouncedHoverEnd=on(t.onHoverEnd,Ee.DEFAULT_HOVER_END_DEBOUNCE_MS)}destroy(){var t,n;(t=this.debouncedHoverStart)==null||t.cancel(),(n=this.debouncedHoverEnd)==null||n.cancel()}};M(Ee,"DEFAULT_HOVER_END_DEBOUNCE_MS",67);let xe=Ee;function qe(e,t){return e.addEventListener("mouseenter",t.handleMouseEnter),e.addEventListener("mouseleave",t.handleMouseLeave),e.addEventListener("mousemove",t.handleMouseMove),{destroy(){e.removeEventListener("mouseenter",t.handleMouseEnter),e.removeEventListener("mouseleave",t.handleMouseLeave),e.removeEventListener("mousemove",t.handleMouseMove)}}}const eo=Symbol("hover-action-context");function go(e=100){const t=_n(!1);Tn(eo,t);const n=new xe({onHoverStart(){t.set(!0)},onHoverEnd(){t.set(!1)},hoverTriggerDuration:e});return function(r){return qe(r,n)}}const Kt=class Kt{constructor(t){M(this,"_state");M(this,"_tippy");M(this,"_triggerElement");M(this,"_contentElement");M(this,"_contentProps");M(this,"_hoverContext");M(this,"_referenceClientRect");M(this,"_hasPointerEvents",!0);M(this,"_setOpen",t=>{var n,r;this._isOpen!==t&&(this._state.update(s=>({...s,open:t})),(r=(n=this._opts).onOpenChange)==null||r.call(n,t))});M(this,"openTooltip",()=>{this.internalControlSetOpen(!0)});M(this,"closeTooltip",()=>{this.internalControlSetOpen(!1)});M(this,"toggleTooltip",()=>{this.internalControlSetOpen(!this._isOpen)});M(this,"externalControlSetOpen",t=>{this._opts.open=t,t!==void 0&&this._setOpen(t)});M(this,"updateTippyTheme",t=>{this._opts.tippyTheme!==t&&(this._opts.tippyTheme=t,this._updateTippy())});M(this,"internalControlSetOpen",t=>{this._isExternallyControlled||this._setOpen(t)});M(this,"_updateTippy",()=>{var n;if(!this._triggerElement||!this._contentElement||!this._contentProps)return(n=this._tippy)==null||n.destroy(),void(this._tippy=void 0);const t={trigger:"manual",showOnCreate:this._isOpen,offset:this._opts.offset??[0,2],interactive:this._hasPointerEvents,content:this._contentElement,popperOptions:{strategy:"fixed",modifiers:[{name:"preventOverflow",options:{padding:this._opts.nested?12:0}}]},duration:0,delay:0,placement:no(this._contentProps),hideOnClick:!1,appendTo:this._opts.nested?this._triggerElement:document.body,theme:this._opts.tippyTheme};if(this._referenceClientRect!==void 0){const r=this._referenceClientRect;t.getReferenceClientRect=()=>r}if(this._tippy!==void 0)this._tippy.setProps(t);else{const r=this._state.subscribe(s=>{var i,a;s.open?(i=this._tippy)==null||i.show():(a=this._tippy)==null||a.hide()});this._tippy=Ut(this._triggerElement,{...t,onDestroy:r})}});M(this,"update",()=>{var t,n;(n=(t=this._tippy)==null?void 0:t.popperInstance)==null||n.update()});M(this,"registerTrigger",(t,n)=>{this._triggerElement=t,this._referenceClientRect=n;const r=this._hoverContext&&qe(this._triggerElement,this._hoverContext);return this._updateTippy(),{update:s=>{this._referenceClientRect=s,this._updateTippy()},destroy:()=>{r==null||r.destroy(),this._triggerElement=void 0,this._updateTippy()}}});M(this,"registerContents",(t,n)=>{t.remove(),this._contentElement=t,this._contentProps=n;const r=this._hoverContext&&qe(this._contentElement,this._hoverContext);this._updateTippy();const s=function(i,a){const c=new ResizeObserver(()=>a());return c.observe(i),()=>c.disconnect()}(t,this.update);return{destroy:()=>{r==null||r.destroy(),this._contentElement=void 0,this._updateTippy(),s()},update:i=>{n={...n,...i},this._contentProps=n,this._updateTippy()}}});M(this,"requestClose",()=>{var t;(t=this._contentElement)==null||t.dispatchEvent(new xt)});this._opts=t,this._state=_n({open:this._opts.open??this._opts.defaultOpen??!1}),this.supportsHover&&(this._hoverContext=new xe({hoverTriggerDuration:this.delayDurationMs,onHoverStart:()=>{this.openTooltip(),this._opts.onHoverStart()},onHoverEnd:()=>{this.closeTooltip(),this._opts.onHoverEnd()}})),this._hasPointerEvents=this._opts.hasPointerEvents??!0}get supportsHover(){return this._opts.triggerOn.includes(Qt.Hover)}get supportsClick(){return this._opts.triggerOn.includes(Qt.Click)}get triggerElement(){return this._triggerElement}get contentElement(){return this._contentElement}get state(){return this._state}get delayDurationMs(){return this._opts.delayDurationMs??Kt.DEFAULT_DELAY_DURATION_MS}get _isExternallyControlled(){const{defaultOpen:t,open:n}=this._opts;return n!==void 0&&(t!==void 0&&console.warn("`defaultOpen` has no effect when `open` is provided"),!0)}get _isOpen(){return ir(this._state).open}};M(Kt,"CONTEXT_KEY","augment-tooltip-context"),M(Kt,"DEFAULT_DELAY_DURATION_MS",160);let Pt=Kt;function no(e){return e.align==="center"?e.side:`${e.side}-${e.align}`}function ro(e,t){te(t,!1);let n=A(t,"defaultOpen",24,()=>{}),r=A(t,"open",24,()=>{}),s=A(t,"onOpenChange",24,()=>{}),i=A(t,"delayDurationMs",24,()=>{}),a=A(t,"nested",8,!0),c=A(t,"hasPointerEvents",8,!0),p=A(t,"offset",24,()=>{}),v=A(t,"onHoverStart",8,()=>{}),d=A(t,"onHoverEnd",8,()=>{}),l=A(t,"triggerOn",24,()=>[Qt.Hover,Qt.Click]);const O=()=>w.openTooltip(),b=()=>w.closeTooltip();let E=A(t,"tippyTheme",24,()=>{});const w=new Pt({defaultOpen:n(),open:r(),onOpenChange:s(),delayDurationMs:i(),nested:a(),onHoverStart:v(),onHoverEnd:d(),triggerOn:l(),tippyTheme:E(),hasPointerEvents:c(),offset:p()});Tn(Pt.CONTEXT_KEY,w),jt(()=>dt(r()),()=>{w.externalControlSetOpen(r())}),jt(()=>dt(E()),()=>{w.updateTippyTheme(E())}),Ie(),Oe();var g=he(),C=Ft(g);return Ct(C,t,"default",{},null),ct(e,g),ye(t,"requestOpen",O),ye(t,"requestClose",b),ee({requestOpen:O,requestClose:b})}var oo=Rt('<div role="button" tabindex="-1"><!></div>');function io(e,t){te(t,!1);let n=A(t,"referenceClientRect",24,()=>{}),r=A(t,"class",8,"");const s=Cn(Pt.CONTEXT_KEY),i=p=>{s.supportsClick&&(s.toggleTooltip(),p.stopPropagation())};Oe();var a=oo(),c=Jt(a);Ct(c,t,"default",{},null),me(()=>X("click",a,i)),me(()=>X("keydown",a,function(p){rt.call(this,t,p)})),An(a,(p,v)=>{var d;return(d=s.registerTrigger)==null?void 0:d.call(s,p,v)},n),ge(()=>Dn(a,1,`l-tooltip-trigger ${r()}`,"svelte-i540k2")),ct(e,a),ee()}var ao=Rt('<div role="button" tabindex="-1"><!></div>');function so(e,t){te(t,!1);const[n,r]=cr(),s=()=>Je(O,"$state",n),i=()=>Je(C,"$openState",n);let a=A(t,"onEscapeKeyDown",8,()=>{}),c=A(t,"onClickOutside",8,()=>{}),p=A(t,"onRequestClose",8,()=>{}),v=A(t,"side",8,"top"),d=A(t,"align",8,"center");const l=Cn(Pt.CONTEXT_KEY),O=l.state,b=f=>{f.target!==null&&f.target instanceof Node&&l.contentElement&&l.triggerElement&&s().open&&(f.composedPath().includes(l.contentElement)||f.composedPath().includes(l.triggerElement)||(l.closeTooltip(),c()(f)))},E=f=>{f.target!==null&&f.target instanceof Node&&l.contentElement&&s().open&&f.key==="Escape"&&(l.closeTooltip(),a()(f))},w=f=>{var x;if(xt.isEvent(f)&&f.target&&((x=l.contentElement)!=null&&x.contains(f.target)))return l.closeTooltip(),p()(f),void f.stopPropagation()},g=f=>{f.target===window&&l.requestClose()},C=ar(O,f=>f.open);sr(()=>{var f;(f=l.contentElement)==null||f.removeEventListener(xt.eventType,w)}),jt(()=>(i(),xt),()=>{l.contentElement&&(i()?l.contentElement.addEventListener(xt.eventType,w):l.contentElement.removeEventListener(xt.eventType,w))}),Ie(),Oe();var h=ao();let o;X("click",Ce,function(...f){var x;(x=s().open?b:void 0)==null||x.apply(this,f)},!0),X("keydown",Ce,function(...f){var x;(x=s().open?E:void 0)==null||x.apply(this,f)},!0),X("blur",Ce,function(...f){var x;(x=s().open?g:void 0)==null||x.apply(this,f)},!0);var m=Jt(h);Ct(m,t,"default",{},null),An(h,(f,x)=>{var _;return(_=l.registerContents)==null?void 0:_.call(l,f,x)},()=>({side:v(),align:d()})),me(()=>X("click",h,en(function(f){rt.call(this,t,f)}))),me(()=>X("keydown",h,en(function(f){rt.call(this,t,f)}))),ge(f=>{o=Dn(h,1,"l-tooltip-contents svelte-1mcoenu",null,o,f),Ze(h,"data-position-side",v()),Ze(h,"data-position-align",d())},[()=>({"l-tooltip-contents--open":s().open})],Se),ct(e,h),ee(),r()}const Me={Root:ro,Trigger:io,Content:so};var co=Rt('<div class="svelte-hdzv5n"><!></div>'),uo=Rt("<!> <!>",1);function yo(e,t){const n=ur(t);te(t,!1);let r=A(t,"content",24,()=>{}),s=A(t,"width",24,()=>{}),i=A(t,"minWidth",24,()=>{}),a=A(t,"maxWidth",8,"250px"),c=A(t,"delayDurationMs",24,()=>{}),p=A(t,"triggerOn",24,()=>{}),v=A(t,"side",8,"top"),d=A(t,"nested",8,!1),l=A(t,"hasPointerEvents",24,()=>{}),O=A(t,"offset",24,()=>v()==="top"||v()==="bottom"?[0,5]:[5,0]),b=A(t,"open",24,()=>{}),E=A(t,"align",8,"center"),w=A(t,"class",8,""),g=A(t,"onOpenChange",24,()=>{}),C=A(t,"referenceClientRect",24,()=>{}),h=A(t,"theme",8,""),o=de(void 0);const m=()=>{var _;return(_=st(o))==null?void 0:_.requestOpen()},f=()=>{var _;return(_=st(o))==null?void 0:_.requestClose()},x=Se(()=>h()||"");return pr(Me.Root(e,{get delayDurationMs(){return c()},get onOpenChange(){return g()},get triggerOn(){return p()},get nested(){return d()},get hasPointerEvents(){return l()},get offset(){return O()},get open(){return b()},get tippyTheme(){return`default text-tooltip-augment ${st(x)??""}`},children:(_,k)=>{var j=uo(),P=Ft(j);Me.Trigger(P,{get referenceClientRect(){return C()},get class(){return w()},children:($,I)=>{var F=he(),S=Ft(F);Ct(S,t,"default",{},null),ct($,F)},$$slots:{default:!0}});var H=fr(P,2),W=$=>{Me.Content($,{get side(){return v()},get align(){return E()},children:(I,F)=>{var S=co();let N;var Q=Jt(S),tt=R=>{var V=he(),pt=Ft(V);Ct(pt,t,"content",{},null),ct(R,V)},et=R=>{dr(R,{size:1,class:"tooltip-text",children:(V,pt)=>{var ft=vr();ge(()=>hr(ft,r())),ct(V,ft)},$$slots:{default:!0}})};He(Q,R=>{Ge(()=>n.content)?R(tt):R(et,!1)}),ge(R=>N=lr(S,"",N,R),[()=>({width:s(),"min-width":i(),"max-width":a()})],Se),ct(I,S)},$$slots:{default:!0}})};He(H,$=>{dt(r()),Ge(()=>r()||n.content)&&$(W)}),ct(_,j)},$$slots:{default:!0},$$legacy:!0}),_=>ve(o,_),()=>st(o)),ye(t,"requestOpen",m),ye(t,"requestClose",f),ee({requestOpen:m,requestClose:f})}var po=Rt("<div><!></div>"),fo=Rt("<div><!></div>");function bo(e,t){const n=Qe(t,["children","$$slots","$$events","$$legacy"]),r=Qe(n,["size","insetContent","variant","interactive","includeBackground","borderless"]);te(t,!1);const s=de(),i=de(),a=de();let c=A(t,"size",8,1),p=A(t,"insetContent",8,!1),v=A(t,"variant",8,"surface"),d=A(t,"interactive",8,!1),l=A(t,"includeBackground",8,!0),O=A(t,"borderless",8,!1);jt(()=>(st(s),dt(r)),()=>{ve(s,r.class)}),jt(()=>(dt(c()),dt(v()),dt(p()),dt(d()),dt(l()),dt(O()),st(s)),()=>{ve(i,["c-card",`c-card--size-${c()}`,`c-card--${v()}`,p()?"c-card--insetContent":"",d()?"c-card--interactive":"",l()?"c-card--with-background":"",O()?"c-card--borderless":"",st(s)])}),jt(()=>st(i),()=>{ve(a,{...mr("accent"),class:st(i).join(" ")})}),Ie(),Oe();var b=he(),E=Ft(b),w=C=>{var h=po();tn(h,()=>({...st(a),role:"button",tabindex:"0"}),void 0,"svelte-1wfjrcd");var o=Jt(h);Ct(o,t,"default",{},null),X("click",h,function(m){rt.call(this,t,m)}),X("keyup",h,function(m){rt.call(this,t,m)}),X("keydown",h,function(m){rt.call(this,t,m)}),X("mousedown",h,function(m){rt.call(this,t,m)}),X("mouseover",h,function(m){rt.call(this,t,m)}),X("focus",h,function(m){rt.call(this,t,m)}),X("mouseleave",h,function(m){rt.call(this,t,m)}),X("blur",h,function(m){rt.call(this,t,m)}),X("contextmenu",h,function(m){rt.call(this,t,m)}),ct(C,h)},g=C=>{var h=fo();tn(h,()=>({...st(a)}),void 0,"svelte-1wfjrcd");var o=Jt(h);Ct(o,t,"default",{},null),ct(C,h)};He(E,C=>{d()?C(w):C(g,!1)}),ct(e,b),ee()}export{bo as C,xe as H,ro as R,yo as T,Qt as a,ye as b,xt as c,on as d,Pt as e,so as f,io as g,go as h,qe as o,mo as p,en as s,Ut as t};
