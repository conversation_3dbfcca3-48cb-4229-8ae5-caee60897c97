import{c as P,E as Q,n as U,p as Y,r as aa,ab as sa,ac as na,ad as ea,ae as la,af as ta,ag as G,z as ia,A as ca,C as f,W as d,_ as ra,D as oa,T as pa,F as H,G as C,b as u,I as va,a as fa,R as ua,J as w,H as $,L as J,M as k,O as K,V as L,Z as r,m as h,t as m,N as M,a2 as da,aa as ha,X as g,K as O}from"./SpinnerAugment-C3d3R_8C.js";import{n as ma,g as ga,a as _a}from"./file-paths-BEJIrsZp.js";function ba(_,s,b,i,I,x){var o,p,l,t=null,E=_;P(()=>{const e=s()||null;var c=e==="svg"?na:null;e!==o&&(l&&(e===null?Y(l,()=>{l=null,p=null}):e===p?aa(l):(sa(l),G(!1))),e&&e!==p&&(l=U(()=>{if(t=c?document.createElementNS(c,e):document.createElement(e),ea(t,t),i){var z=t.appendChild(la());i(t,z)}ta.nodes_end=t,E.before(t)})),(o=e)&&(p=o),G(!0))},Q)}var Ia=w('<div><div class="c-filespan__dir-text svelte-9pfhnp"> </div></div>'),xa=w('<span class="right-icons svelte-9pfhnp"><!></span>'),Ea=w('<!> <span class="c-filespan__filename svelte-9pfhnp"> </span> <!> <!>',1);function ka(_,s){const b=ia(s);ca(s,!1);const i=h(),I=h(),x=h(),o=h();let p=f(s,"class",8,""),l=f(s,"filepath",8),t=f(s,"size",8,1),E=f(s,"nopath",8,!1),e=f(s,"growname",8,!0),c=f(s,"onClick",24,()=>{});d(()=>O(l()),()=>{g(i,ma(l()))}),d(()=>r(i),()=>{g(I,ga(r(i)))}),d(()=>r(i),()=>{g(x,_a(r(i)))}),d(()=>O(c()),()=>{g(o,c()?"button":"div")}),ra(),oa(),pa(_,{get size(){return t()},children:(z,za)=>{var F=H();ba(C(F),()=>r(o),0,(N,R)=>{fa(N,()=>({class:`c-filespan ${p()}`,role:c()?"button":"",tabindex:"0"}),void 0,"svelte-9pfhnp"),ua("click",N,function(...a){var n;(n=c())==null||n.apply(this,a)});var V=Ea(),y=C(V),S=a=>{var n=H(),v=C(n);M(v,s,"leftIcon",{},null),u(a,n)};$(y,a=>{J(()=>b.leftIcon)&&a(S)});var A=k(y,2),T=m(A),D=k(A,2),Z=a=>{var n=Ia();let v;var X=m(n),q=m(X);K(B=>{v=ha(n,1,"c-filespan__dir svelte-9pfhnp",null,v,B),L(q,r(x))},[()=>({growname:e()})],da),u(a,n)};$(D,a=>{E()||a(Z)});var j=k(D,2),W=a=>{var n=xa(),v=m(n);M(v,s,"rightIcon",{},null),u(a,n)};$(j,a=>{J(()=>b.rightIcon)&&a(W)}),K(()=>L(T,r(I))),u(R,V)}),u(z,F)},$$slots:{default:!0}}),va()}export{ka as F,ba as e};
