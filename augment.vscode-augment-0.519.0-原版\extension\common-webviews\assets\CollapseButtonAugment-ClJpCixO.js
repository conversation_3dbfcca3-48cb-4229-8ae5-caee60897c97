import{i as $e,aB as ye,aC as be,c as we,p as xe,n as ke,U as _e,l as x,f as X,a as Z,t as c,b as p,v as Ce,u as Le,z as Se,A as ce,C as M,w as oe,aD as ze,W as w,_ as He,D as ie,J as N,N as E,H as Q,O as Ie,a2 as Me,a3 as W,aa as A,Z as K,m as le,Q as Te,I as re,a1 as de,M as ne,K as T,X as P,G as ve,L as Be,a6 as Fe,F as Ue}from"./SpinnerAugment-C3d3R_8C.js";import{h as G,a as Ae,I as Ee}from"./IconButtonAugment-BYROpfM6.js";import{b as Ne}from"./CardAugment-L1_52yiK.js";function Ve(o,e,l){var s,a=o,u=_e,h=$e()?ye:be;we(()=>{h(u,u=e())&&(s&&xe(s),s=ke(()=>l(a)))})}var Oe=X("<svg><!></svg>");function Ye(o,e){const l=x(e,["children","$$slots","$$events","$$legacy"]);var s=Oe();Z(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...l}));var a=c(s);G(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248.4 84.3c1.6-2.7 4.5-4.3 7.6-4.3s6 1.6 7.6 4.3L461.9 410c1.4 2.3 2.1 4.9 2.1 7.5 0 8-6.5 14.5-14.5 14.5h-387c-8 0-14.5-6.5-14.5-14.5 0-2.7.7-5.3 2.1-7.5zm-41-25L9.1 385c-6 9.8-9.1 21-9.1 32.5C0 452 28 480 62.5 480h387c34.5 0 62.5-28 62.5-62.5 0-11.5-3.2-22.7-9.1-32.5L304.6 59.3C294.3 42.4 275.9 32 256 32s-38.3 10.4-48.6 27.3M288 368a32 32 0 1 0-64 0 32 32 0 1 0 64 0m-8-184c0-13.3-10.7-24-24-24s-24 10.7-24 24v96c0 13.3 10.7 24 24 24s24-10.7 24-24z"/>',!0),p(o,s)}var De=X("<svg><!></svg>");function Je(o,e){const l=x(e,["children","$$slots","$$events","$$legacy"]);var s=De();Z(s,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...l}));var a=c(s);G(a,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M239 498.7c-8.8 7-21.2 7-30 0l-160-128c-10.4-8.3-12-23.4-3.7-33.7s23.4-12 33.7-3.8l145 116 145-116c10.3-8.3 25.5-6.6 33.7 3.8s6.6 25.5-3.7 33.7zm160-357.4c10.4 8.3 12 23.4 3.8 33.7s-23.4 12-33.7 3.7L224 62.7l-145 116c-10.4 8.3-25.5 6.6-33.7-3.7s-6.6-25.5 3.7-33.7l160-128c8.8-7 21.2-7 30 0z"/>',!0),p(o,s)}const pe=Symbol("collapsible");function Ke(){return Le(pe)}var Pe=N('<footer class="c-collapsible__footer svelte-gbhym3"><!></footer>'),Qe=N('<div class="c-collapsible__body svelte-gbhym3"><!></div> <!>',1),We=N('<div><header><div><!></div></header> <div><div class="c-collapsible__content-inner svelte-gbhym3"><!></div></div></div>');function es(o,e){const l=Se(e),s=x(e,["children","$$slots","$$events","$$legacy"]),a=x(s,["toggle","collapsed","stickyHeader","expandable","isHeaderStuck","stickyHeaderTop"]);ce(e,!1);const[u,h]=de(),d=()=>W(v,"$collapsedStore",u),k=()=>W(H,"$expandableStore",u),S=le();let m=M(e,"collapsed",12,!1),B=M(e,"stickyHeader",8,!1),r=M(e,"expandable",12,!0),z=M(e,"isHeaderStuck",12,!1),_=M(e,"stickyHeaderTop",24,()=>-.5);const v=oe(m()),O=ze(v,n=>n),H=oe(r());let i,D=le(!1);function R(n){r()?v.set(n):v.set(!0)}const J=function(){R(!d())};Ce(pe,{collapsed:O,setCollapsed:R,toggle:J,expandable:H}),w(()=>k(),()=>{r(k())}),w(()=>T(r()),()=>{H.set(r())}),w(()=>d(),()=>{m(d())}),w(()=>T(m()),()=>{v.set(m())}),w(()=>T(r()),()=>{r()||v.set(!0)}),w(()=>T(m()),()=>{m()?(clearTimeout(i),i=setTimeout(()=>{P(D,!1)},200)):(clearTimeout(i),P(D,!0))}),w(()=>(K(S),T(a)),()=>{P(S,a.class)}),He(),ie();var F=We();let j,q;var U=c(F);let V;var Y=c(U);let ee;var ue=c(Y);E(ue,e,"header",{},null),Ae(U,(n,C)=>function(g,f){const{onStuck:$,onUnstuck:I,offset:y=0}=f,t=document.createElement("div");t.style.position="absolute",t.style.top=y?`${y}px`:"0",t.style.height="1px",t.style.width="100%",t.style.pointerEvents="none",t.style.opacity="0",t.style.zIndex="-1";const L=g.parentNode;if(!L)return{update:()=>{},destroy:()=>{}};window.getComputedStyle(L).position==="static"&&(L.style.position="relative"),L.insertBefore(t,g);const ae=new IntersectionObserver(([b])=>{b.isIntersecting?I==null||I():$==null||$()},{threshold:0,rootMargin:"-1px 0px 0px 0px"});return ae.observe(t),{update(b){f.onStuck=b.onStuck,f.onUnstuck=b.onUnstuck,b.offset!==void 0&&b.offset!==y&&(t.style.top=`${b.offset}px`)},destroy(){ae.disconnect(),t.remove()}}}(n,C),()=>({offset:-_(),onStuck:()=>{z(!0)},onUnstuck:()=>{z(!1)}}));var se=ne(U,2);let te;var he=c(se),me=c(he),ge=n=>{var C=Qe(),g=ve(C),f=c(g);E(f,e,"default",{},null);var $=ne(g,2),I=y=>{var t=Pe(),L=c(t);E(L,e,"footer",{},null),p(y,t)};Q($,y=>{Be(()=>l.footer)&&y(I)}),p(n,C)};Q(me,n=>{k()&&K(D)&&n(ge)}),Ie((n,C,g,f,$)=>{j=A(F,1,`c-collapsible ${K(S)??""}`,"svelte-gbhym3",j,n),q=Te(F,"",q,C),V=A(U,1,"c-collapsible__header svelte-gbhym3",null,V,g),ee=A(Y,1,"c-collapsible__header-inner svelte-gbhym3",null,ee,f),te=A(se,1,"c-collapsible__content svelte-gbhym3",null,te,$)},[()=>({"is-collapsed":d(),"is-expandable":k()}),()=>({"--sticky-header-top":`${_()}px`}),()=>({"is-sticky":B()}),()=>({"is-collapsed":d(),"is-header-stuck":z(),"has-header-padding":_()>0}),()=>({"is-collapsed":d()})],Me),p(o,F),Ne(e,"toggle",J);var fe=re({toggle:J});return h(),fe}var Xe=X("<svg><!></svg>");function Ze(o,e){const l=x(e,["children","$$slots","$$events","$$legacy"]);var s=Xe();Z(s,()=>({xmlns:"http://www.w3.org/2000/svg",width:"8",height:"10","data-ds-icon":"fa",viewBox:"0 0 8 10",...l}));var a=c(s);G(a,()=>'<path d="M4.451 6.357a.42.42 0 0 0-.527 0L1.11 8.607a.42.42 0 0 0-.065.592.424.424 0 0 0 .593.067l2.548-2.04 2.55 2.04a.423.423 0 0 0 .527-.66zm2.813-4.965a.422.422 0 1 0-.526-.658l-2.55 2.04-2.55-2.04a.421.421 0 1 0-.527.658l2.813 2.25a.42.42 0 0 0 .527 0z"/>',!0),p(o,s)}var Ge=N('<span class="c-collapse-button-augment__icon svelte-hw7s17"><!></span>');function ss(o,e){const l=x(e,["children","$$slots","$$events","$$legacy"]),s=x(l,[]);ce(e,!1);const[a,u]=de(),h=()=>W(d,"$collapsed",a),{collapsed:d,setCollapsed:k}=Ke();ie(),Ee(o,Fe({variant:"ghost-block",color:"neutral",size:1},()=>s,{$$events:{click:function(){k(!h())}},children:(S,m)=>{var B=Ge(),r=c(B);E(r,e,"default",{get collapsed(){return h()}},z=>{var _=Ue(),v=ve(_),O=i=>{Je(i,{})},H=i=>{Ze(i,{})};Q(v,i=>{h()?i(O):i(H,!1)}),p(z,_)}),p(S,B)},$$slots:{default:!0}})),re(),u()}export{Je as A,es as C,Ye as T,ss as a,Ze as b,Ke as g,Ve as k};
