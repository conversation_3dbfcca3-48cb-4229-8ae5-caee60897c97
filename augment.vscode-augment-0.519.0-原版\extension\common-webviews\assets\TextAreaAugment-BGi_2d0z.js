import{l as j,A as at,C as i,m as g,W as V,_ as st,D as lt,J as O,F as W,G as N,H as U,b as $,X as c,Z as s,a2 as vt,a6 as tt,N as R,M as F,t as Y,I as nt,L as it,K as H,Y as rt,z as ht,aj as dt,a as ft,a0 as pt,al as gt,ap as p,R as m,O as $t,P as mt,V as yt}from"./SpinnerAugment-C3d3R_8C.js";import{I as wt,b as l,a as xt}from"./IconButtonAugment-BYROpfM6.js";import{T as bt,a as et}from"./CardAugment-L1_52yiK.js";import{B as kt}from"./ButtonAugment-Cfj8nOxE.js";import{B as Ct,b as Lt}from"./BaseTextInput-hjj6nBZd.js";var Tt=O("<!> <!> <!>",1),Rt=O('<div class="c-successful-button svelte-1dvyzw2"><!></div>');function Vt(G,t){var a;const J=j(t,["children","$$slots","$$events","$$legacy"]),A=j(J,["defaultColor","tooltip","stateVariant","onClick","tooltipDuration","icon","stickyColor","persistOnTooltipClose","tooltipNested"]);at(t,!1);const y=g(),w=g(),L=g();let x,b=i(t,"defaultColor",8),D=i(t,"tooltip",24,()=>{}),q=i(t,"stateVariant",24,()=>{}),P=i(t,"onClick",8),_=i(t,"tooltipDuration",8,1500),u=i(t,"icon",8,!1),B=i(t,"stickyColor",8,!0),X=i(t,"persistOnTooltipClose",8,!1),K=i(t,"tooltipNested",24,()=>{}),r=g("neutral"),k=g(b()),E=g(void 0),M=g((a=D())==null?void 0:a.neutral);async function S(o){var h;try{c(r,await P()(o)??"neutral")}catch{c(r,"failure")}c(M,(h=D())==null?void 0:h[s(r)]),clearTimeout(x),x=setTimeout(()=>{var f;(f=s(E))==null||f(),B()||c(r,"neutral")},_())}V(()=>(s(y),s(w),H(A)),()=>{c(y,A.variant),c(w,rt(A,["variant"]))}),V(()=>(H(q()),s(r),s(y)),()=>{var o;c(L,((o=q())==null?void 0:o[s(r)])??s(y))}),V(()=>(s(r),H(b())),()=>{s(r)==="success"?c(k,"success"):s(r)==="failure"?c(k,"error"):c(k,b())}),st(),lt();var v=Rt(),T=Y(v);const n=vt(()=>(H(et),it(()=>[et.Hover])));bt(T,{onOpenChange:function(o){var h;X()||o||(clearTimeout(x),x=void 0,c(M,(h=D())==null?void 0:h.neutral),B()||c(r,"neutral"))},get content(){return s(M)},get triggerOn(){return s(n)},get nested(){return K()},get requestClose(){return s(E)},set requestClose(o){c(E,o)},children:(o,h)=>{var f=W(),Z=N(f),ct=I=>{wt(I,tt(()=>s(w),{get color(){return s(k)},get variant(){return s(L)},$$events:{click:S,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,Q)=>{var d=Tt(),C=N(d);R(C,t,"iconLeft",{},null);var z=F(C,2);R(z,t,"default",{},null);var ut=F(z,2);R(ut,t,"iconRight",{},null),$(e,d)},$$slots:{default:!0}}))},ot=I=>{kt(I,tt(()=>s(w),{get color(){return s(k)},get variant(){return s(L)},$$events:{click:S,keyup(e){l.call(this,t,e)},keydown(e){l.call(this,t,e)},mousedown(e){l.call(this,t,e)},mouseover(e){l.call(this,t,e)},focus(e){l.call(this,t,e)},mouseleave(e){l.call(this,t,e)},blur(e){l.call(this,t,e)},contextmenu(e){l.call(this,t,e)}},children:(e,Q)=>{var d=W(),C=N(d);R(C,t,"default",{},null),$(e,d)},$$slots:{default:!0,iconLeft:(e,Q)=>{var d=W(),C=N(d);R(C,t,"iconLeft",{},null),$(e,d)},iconRight:(e,Q)=>{var d=W(),C=N(d);R(C,t,"iconRight",{},null),$(e,d)}}}))};U(Z,I=>{u()?I(ct):I(ot,!1)}),$(o,f)},$$slots:{default:!0},$$legacy:!0}),$(G,v),nt()}var Ht=O('<label class="c-text-area-label svelte-c1sr7w"> </label>'),_t=O('<div class="c-text-area-label-container svelte-c1sr7w"><!> <!></div>'),Et=O("<textarea></textarea>"),It=O('<div class="c-text-area svelte-c1sr7w"><!> <!></div>');function qt(G,t){const J=ht(t),A=j(t,["children","$$slots","$$events","$$legacy"]),y=j(A,["label","variant","size","color","resize","textInput","type","value","id"]);at(t,!1);const w=g(),L=g(),x=g();let b=i(t,"label",24,()=>{}),D=i(t,"variant",8,"surface"),q=i(t,"size",8,2),P=i(t,"color",24,()=>{}),_=i(t,"resize",8,"none"),u=i(t,"textInput",28,()=>{}),B=i(t,"type",8,"default"),X=i(t,"value",12,""),K=i(t,"id",24,()=>{});function r(){if(!u())return;u(u().style.height="auto",!0);const v=.8*window.innerHeight,T=Math.min(u().scrollHeight,v);u(u().style.height=`${T}px`,!0),u(u().style.overflowY=u().scrollHeight>v?"auto":"hidden",!0)}dt(()=>{if(u()){r();const v=()=>r();return window.addEventListener("resize",v),()=>{window.removeEventListener("resize",v)}}}),V(()=>H(K()),()=>{c(w,K()||`text-field-${Math.random().toString(36).substring(2,11)}`)}),V(()=>(s(L),s(x),H(y)),()=>{c(L,y.class),c(x,rt(y,["class"]))}),st(),lt();var k=It(),E=Y(k),M=v=>{var T=_t(),n=Y(T),a=h=>{var f=Ht(),Z=Y(f);$t(()=>{mt(f,"for",s(w)),yt(Z,b())}),$(h,f)};U(n,h=>{b()&&h(a)});var o=F(n,2);R(o,t,"topRightAction",{},null),$(v,T)};U(E,v=>{H(b()),it(()=>b()||J.topRightAction)&&v(M)});var S=F(E,2);Ct(S,{get type(){return B()},get variant(){return D()},get size(){return q()},get color(){return P()},children:(v,T)=>{var n=Et();ft(n,a=>({id:s(w),spellCheck:"false",class:`c-text-area__input c-base-text-input__input ${s(L)}`,...s(x),[pt]:a}),[()=>({"c-textarea--resize-none":_()==="none","c-textarea--resize-both":_()==="both","c-textarea--resize-horizontal":_()==="horizontal","c-textarea--resize-vertical":_()==="vertical"})],"svelte-c1sr7w"),gt(n,a=>u(a),()=>u()),p(()=>Lt(n,X)),xt(n,a=>function(o){r();const h=()=>r();return o.addEventListener("input",h),setTimeout(r,0),{destroy(){o.removeEventListener("input",h)}}}(a)),p(()=>m("click",n,function(a){l.call(this,t,a)})),p(()=>m("focus",n,function(a){l.call(this,t,a)})),p(()=>m("keydown",n,function(a){l.call(this,t,a)})),p(()=>m("change",n,function(a){l.call(this,t,a)})),p(()=>m("input",n,function(a){l.call(this,t,a)})),p(()=>m("keyup",n,function(a){l.call(this,t,a)})),p(()=>m("blur",n,function(a){l.call(this,t,a)})),p(()=>m("select",n,function(a){l.call(this,t,a)})),p(()=>m("mouseup",n,function(a){l.call(this,t,a)})),$(v,n)},$$slots:{default:!0}}),$(G,k),nt()}export{Vt as S,qt as T};
