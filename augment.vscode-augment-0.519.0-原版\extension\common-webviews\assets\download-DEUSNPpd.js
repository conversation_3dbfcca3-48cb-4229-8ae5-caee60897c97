var ae=Object.defineProperty;var oe=(o,e,s)=>e in o?ae(o,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):o[e]=s;var h=(o,e,s)=>oe(o,typeof e!="symbol"?e+"":e,s);import{f as _,b as r,l as $,A as X,u as ne,C as l,W as Y,X as T,m as z,Y as ie,K as B,Z as g,_ as J,D as ce,J as P,a6 as le,F as re,G as W,N as de,a2 as V,t as v,O as H,aa as k,L as ue,I as j,H as he,M as R,P as ge,R as Z,V as K,w as me,aA as E,a as I}from"./SpinnerAugment-C3d3R_8C.js";import{B as ve,a as pe}from"./user-BS1qxHV9.js";import{C as we,b as c,h as O}from"./IconButtonAugment-BYROpfM6.js";import{a as Ce}from"./BaseTextInput-hjj6nBZd.js";import{i as F}from"./index-BWYp8-tu.js";var fe=_('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z" fill="currentColor"></path></svg>');function He(o){var e=fe();r(o,e)}var be=P("<div><!></div>");const De={Root:pe,IconButton:function(o,e){const s=$(e,["children","$$slots","$$events","$$legacy"]),t=$(s,["color","highContrast","disabled"]);X(e,!1);const i=z(),y=z(),p=ne(ve.CONTEXT_KEY);let w=l(e,"color",24,()=>{return a=p.color,n="neutral",typeof a=="string"&&["accent","neutral","error","success","warning","info"].includes(a)?a:n;var a,n}),C=l(e,"highContrast",8,!1),f=l(e,"disabled",8,!1),S=p.size===0?.5:p.size;Y(()=>(g(i),g(y),B(t)),()=>{T(i,t.class),T(y,ie(t,["class"]))}),J(),ce();var b=be(),L=v(b);const d=V(()=>`c-badge-icon-btn__base-btn ${g(i)}`);we(L,le({get size(){return S},variant:"ghost",get color(){return w()},get highContrast(){return C()},get disabled(){return f()},get class(){return g(d)}},()=>g(y),{$$events:{click(a){c.call(this,e,a)},keyup(a){c.call(this,e,a)},keydown(a){c.call(this,e,a)},mousedown(a){c.call(this,e,a)},mouseover(a){c.call(this,e,a)},focus(a){c.call(this,e,a)},mouseleave(a){c.call(this,e,a)},blur(a){c.call(this,e,a)},contextmenu(a){c.call(this,e,a)}},children:(a,n)=>{var u=re(),x=W(u);de(x,e,"default",{},null),r(a,u)},$$slots:{default:!0}})),H(()=>k(b,1,ue(()=>`c-badge-icon-btn c-badge-icon-btn--${p.variant} c-badge-icon-btn--size-${S}`),"svelte-1im94um")),r(o,b),j()}};var ye=P("<span> </span> <span> </span>",1),Se=P('<label><!> <input type="checkbox" role="switch"/></label>');function Pe(o,e){X(e,!1);const s=z();let t=l(e,"checked",12,!1),i=l(e,"disabled",8,!1),y=l(e,"size",8,2),p=l(e,"ariaLabel",24,()=>{}),w=l(e,"onText",24,()=>{}),C=l(e,"offText",24,()=>{});Y(()=>(B(w()),B(C())),()=>{T(s,w()||C())}),J();var f=Se();let S;var b=v(f),L=n=>{var u=ye(),x=W(u);let N;var q=v(x),U=R(x,2);let G;var ee=v(U);H((te,se)=>{N=k(x,1,"c-toggle-text c-toggle-text--off svelte-xr5g0k",null,N,te),K(q,C()||""),G=k(U,1,"c-toggle-text c-toggle-text--on svelte-xr5g0k",null,G,se),K(ee,w()||"")},[()=>({visible:!t()&&C()}),()=>({visible:t()&&w()})],V),r(n,u)};he(b,n=>{g(s)&&n(L)});var d=R(b,2);let a;H((n,u)=>{S=k(f,1,`c-toggle-track c-toggle-track-size--${y()??""}`,"svelte-xr5g0k",S,n),a=k(d,1,"c-toggle-input svelte-xr5g0k",null,a,u),d.disabled=i(),ge(d,"aria-label",p())},[()=>({checked:t(),disabled:i(),"has-text":g(s)}),()=>({disabled:i()})],V),Ce(d,t),Z("keydown",d,function(n){i()||n.key!=="Enter"&&n.key!==" "||(n.preventDefault(),t(!t()))}),Z("change",f,function(n){c.call(this,e,n)}),r(o,f),j()}const M={enabled:!1,volume:.5},Ie={enabled:!0},xe=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var Q=(o=>(o.AGENT_COMPLETE="agent-complete",o))(Q||{});const ke={"agent-complete":xe},m=class m{constructor(){h(this,"audioCache",new Map)}static getInstance(){return m._instance||(m._instance=new m),m._instance}retrieveAudioElement(e,s){let t=this.audioCache.get(e);return t?t.volume=s.volume:(t=new Audio,t.src=ke[e],t.volume=s.volume,t.preload="auto",t._isUnlocked=!1,this.audioCache.set(e,t)),t}async playSound(e,s){if(s.enabled)try{const t=this.retrieveAudioElement(e,s);t.currentTime=0,await t.play()}catch(t){if(t instanceof DOMException&&t.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",t)}}async unlockSoundForConfig(e){if(!e.enabled)return;const s=this.retrieveAudioElement("agent-complete",e);if(!s._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),s._isUnlocked=!0}catch(t){console.warn("Failed to unlock sound:",t)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};h(m,"_instance");let D=m;const A=D.getInstance();class $e{constructor(e){h(this,"_soundSettings",me(M));h(this,"_isLoaded",!1);h(this,"dispose",()=>{A.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:F.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){E(this._soundSettings).enabled&&A.unlockSoundForConfig(E(this._soundSettings))}async playAgentComplete(){const e=E(this._soundSettings);await A.playSound(Q.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:F.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(M),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:F.updateSoundSettings,data:e}),this._soundSettings.update(s=>({...s,...e}))}catch(s){throw console.error("Failed to update sound settings:",s),s}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const s=Math.max(0,Math.min(1,e));await this.updateSettings({volume:s})}async resetToDefaults(){await this.updateSettings(M)}updateEnabled(e){this.setEnabled(e).catch(s=>{console.error("Failed to update enabled setting:",s)})}updateVolume(e){this.setVolume(e).catch(s=>{console.error("Failed to update volume setting:",s)})}}h($e,"key","soundModel");var _e=_("<svg><!></svg>");function Oe(o,e){const s=$(e,["children","$$slots","$$events","$$legacy"]);var t=_e();I(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var i=v(t);O(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M384 336H192c-8.8 0-16-7.2-16-16V64c0-8.8 7.2-16 16-16h140.1l67.9 67.9V320c0 8.8-7.2 16-16 16m-192 48h192c35.3 0 64-28.7 64-64V115.9c0-12.7-5.1-24.9-14.1-33.9l-67.8-67.9c-9-9-21.2-14.1-33.9-14.1H192c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64M64 128c-35.3 0-64 28.7-64 64v256c0 35.3 28.7 64 64 64h192c35.3 0 64-28.7 64-64v-32h-48v32c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16V192c0-8.8 7.2-16 16-16h32v-48z"/>',!0),r(o,t)}var Le=_("<svg><!></svg>");function Ne(o,e){const s=$(e,["children","$$slots","$$events","$$legacy"]);var t=Le();I(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 448 512",...s}));var i=v(t);O(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',!0),r(o,t)}var Ee=_('<svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M7.49933 0.25C3.49635 0.25 0.25 3.49593 0.25 7.50024C0.25 10.703 2.32715 13.4206 5.2081 14.3797C5.57084 14.446 5.70302 14.2222 5.70302 14.0299C5.70302 13.8576 5.69679 13.4019 5.69323 12.797C3.67661 13.235 3.25112 11.825 3.25112 11.825C2.92132 10.9874 2.44599 10.7644 2.44599 10.7644C1.78773 10.3149 2.49584 10.3238 2.49584 10.3238C3.22353 10.375 3.60629 11.0711 3.60629 11.0711C4.25298 12.1788 5.30335 11.8588 5.71638 11.6732C5.78225 11.205 5.96962 10.8854 6.17658 10.7043C4.56675 10.5209 2.87415 9.89918 2.87415 7.12104C2.87415 6.32925 3.15677 5.68257 3.62053 5.17563C3.54576 4.99226 3.29697 4.25521 3.69174 3.25691C3.69174 3.25691 4.30015 3.06196 5.68522 3.99973C6.26337 3.83906 6.8838 3.75895 7.50022 3.75583C8.1162 3.75895 8.73619 3.83906 9.31523 3.99973C10.6994 3.06196 11.3069 3.25691 11.3069 3.25691C11.7026 4.25521 11.4538 4.99226 11.3795 5.17563C11.8441 5.68257 12.1245 6.32925 12.1245 7.12104C12.1245 9.9063 10.4292 10.5192 8.81452 10.6985C9.07444 10.9224 9.30633 11.3648 9.30633 12.0413C9.30633 13.0102 9.29742 13.7922 9.29742 14.0299C9.29742 14.2239 9.42828 14.4496 9.79591 14.3788C12.6746 13.4179 14.75 10.7025 14.75 7.50024C14.75 3.49593 11.5036 0.25 7.49933 0.25Z" fill="currentColor"></path></svg>');function Ue(o){var e=Ee();r(o,e)}var Fe=_("<svg><!></svg>");function Ge(o,e){const s=$(e,["children","$$slots","$$events","$$legacy"]);var t=Fe();I(t,()=>({xmlns:"http://www.w3.org/2000/svg","data-ds-icon":"fa",viewBox:"0 0 512 512",...s}));var i=v(t);O(i,()=>'<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',!0),r(o,t)}export{De as B,He as C,Ie as D,Ue as G,Ne as P,$e as S,Pe as T,Oe as a,Ge as b};
