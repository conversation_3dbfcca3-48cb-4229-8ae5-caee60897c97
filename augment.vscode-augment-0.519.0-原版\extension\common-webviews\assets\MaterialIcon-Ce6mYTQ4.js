import{A as w,C as t,W as o,_ as d,J as k,O as A,aa as I,Q as L,Z as r,m as n,P as j,V as x,t as y,b as C,I as D,X as e,K as c}from"./SpinnerAugment-C3d3R_8C.js";var F=k("<span> </span>");function J(f,a){w(a,!1);let g=t(a,"class",8,""),p=t(a,"iconName",8,""),l=t(a,"fill",8,!1),m=t(a,"grade",8,"normal"),v=t(a,"title",24,()=>{}),h=n(),b=n(),i=n();o(()=>c(l()),()=>{e(h,l()?"1":"0")}),o(()=>c(l()),()=>{e(b,l()?"700":"400")}),o(()=>c(m()),()=>{switch(m()){case"low":e(i,"-25");break;case"normal":e(i,"0");break;case"high":e(i,"200")}}),d();var s=F(),$=y(s);A(()=>{I(s,1,`material-symbols-outlined ${g()}`,"svelte-htlsjs"),L(s,`font-variation-settings: 'FILL' ${r(h)??""}, 'wght' ${r(b)??""}, 'GRAD' ${r(i)??""};`),j(s,"title",v()),x($,p())}),C(f,s),D()}export{J as M};
