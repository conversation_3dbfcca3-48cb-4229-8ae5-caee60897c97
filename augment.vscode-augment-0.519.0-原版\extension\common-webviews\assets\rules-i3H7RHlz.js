import{A as J,C as ne,m as b,W as L,X as f,K as S,Z as e,_ as ce,D as K,J as F,t as g,M,T as D,S as N,b as n,F as de,G as pe,H as Q,L as ue,I as U,aj as ve,R as me,ak as fe,a1 as ge,w as he,a3 as $e,az as ye}from"./SpinnerAugment-C3d3R_8C.js";import"./design-system-init-s3QUMN0e.js";import{c as R,W as A}from"./IconButtonAugment-BYROpfM6.js";import{B as be}from"./ButtonAugment-Cfj8nOxE.js";import{O as Re}from"./OpenFileButton-CMz0rieg.js";import{C as we,R as ze,E as Ee,T as Me,a as G}from"./index-BWYp8-tu.js";import{M as X,R as Z}from"./message-broker-BVKpqRQ5.js";import{M as Fe}from"./MarkdownEditor-B2OO4mz6.js";import{R as ke}from"./RulesModeSelector-DrwB5ULH.js";import{C as Ce}from"./chevron-left-C6v1hXmN.js";import{T as Te}from"./CardAugment-L1_52yiK.js";import{l as xe}from"./chevron-down-CsTV-WoH.js";import"./chat-context-DL_54yja.js";import"./index-C4gKbsWy.js";import"./index-BiRO5qTg.js";import"./remote-agents-client-Bz3pE78Q.js";import"./types-CGlLNakm.js";import"./ra-diff-ops-model-D15ceLKW.js";import"./TextAreaAugment-BGi_2d0z.js";import"./BaseTextInput-hjj6nBZd.js";import"./async-messaging-BeBg25ZO.js";import"./file-paths-BEJIrsZp.js";import"./isObjectLike-DEzymPim.js";var Le=F('<div class="c-rule-config svelte-1r8al3d"><div class="c-rule-field c-rule-field-full-width svelte-1r8al3d"><!> <!></div></div>'),Se=F('<div class="l-file-controls svelte-1r8al3d" slot="header"><div class="l-file-controls-left svelte-1r8al3d"><div class="c-trigger-section svelte-1r8al3d"><!> <!> <!></div></div> <!></div>'),De=F("<div>Loading...</div>"),Ne=F('<div class="c-rules-container svelte-1vbu0zh"><!></div>');ye(function(_,j){J(j,!1);const[P,q]=ge(),O=()=>$e(B,"$rule",P),W=new X(R),B=he(null),V={handleMessageFromExtension(s){const t=s.data;if(t&&t.type===A.loadFile&&t){const h=t.data.content;if(h!==void 0){const a=h.replace(/^\n+/,""),o=G.getDescriptionFrontmatterKey(a),w=G.getRuleTypeFromContent(a),r=G.extractContent(a);B.set({path:t.data.pathName,content:r,type:w,description:o})}}return!0}};ve(()=>{W.registerConsumer(V),R.postMessage({type:A.rulesLoaded})}),K();var H=Ne();me("message",fe,function(...s){var t;(t=W.onMessageFromExtension)==null||t.apply(this,s)});var Y=g(H),ee=s=>{(function(t,h){J(h,!1);const a=b(),o=b(),w=b();let r=ne(h,"rule",12),$=b(r().content),i=b(r().description);const I=new X(R),se=new we,ae=new Ee(R,I,se),re=new ze(I),k=async(l,p)=>{r({...r(),type:l,description:p||e(i)}),p!==void 0&&f(i,p);try{await re.updateRuleContent({type:l,path:e(a),content:e($),description:p||e(i)})}catch(c){console.error("RulesMarkdownEditor: Error in rulesModel.updateRuleContent:",c)}},oe=xe.debounce(k,500),ie=()=>{R.postMessage({type:A.openSettingsPage,data:"guidelines"})};L(()=>S(r()),()=>{f(a,r().path)}),L(()=>S(r()),()=>{f(o,r().type)}),L(()=>(e(a),e(o),e($),e(i)),()=>{f(w,{path:e(a),type:e(o),content:e($),description:e(i)})}),ce(),K(),Fe(t,{saveFunction:()=>k(e(o),e(i)),variant:"surface",size:2,resize:"vertical",class:"markdown-editor",get value(){return e($)},set value(l){f($,l)},children:(l,p)=>{var c=de(),z=pe(c),C=u=>{var y=Le(),T=g(y),E=g(T);D(E,{size:1,class:"c-field-label",children:(v,m)=>{var x=N("Description");n(v,x)},$$slots:{default:!0}});var d=M(E,2);Me(d,{placeholder:"When should this rules file be fetched by the Agent?",size:1,get value(){return e(i)},set value(v){f(i,v)},$$events:{input:()=>oe(e(o),e(i))},$$legacy:!0}),n(u,y)};Q(z,u=>{e(o),S(Z),ue(()=>e(o)===Z.AGENT_REQUESTED)&&u(C)}),n(l,c)},$$slots:{default:!0,header:(l,p)=>{var c=Se(),z=g(c),C=g(z),u=g(C);Te(u,{content:"Navigate back to all Rules & Guidelines",children:(d,v)=>{be(d,{size:1,variant:"ghost-block",color:"neutral",class:"c-back-button",$$events:{click:ie},$$slots:{iconLeft:(m,x)=>{Ce(m,{slot:"iconLeft"})}}})},$$slots:{default:!0}});var y=M(u,2);D(y,{size:1,class:"c-field-label",children:(d,v)=>{var m=N("Trigger:");n(d,m)},$$slots:{default:!0}});var T=M(y,2);ke(T,{onSave:k,get rule(){return e(w)}});var E=M(z,2);Re(E,{size:1,get path(){return e(a)},onOpenLocalFile:async()=>(ae.openFile({repoRoot:"",pathName:e(a)}),"success"),$$slots:{text:(d,v)=>{D(d,{slot:"text",size:1,children:(m,x)=>{var le=N("Open file");n(m,le)},$$slots:{default:!0}})}}}),n(l,c)}},$$legacy:!0}),U()})(s,{get rule(){return O()}})},te=s=>{var t=De();n(s,t)};Q(Y,s=>{O()!==null?s(ee):s(te,!1)}),n(_,H),U(),q()},{target:document.getElementById("app")});
